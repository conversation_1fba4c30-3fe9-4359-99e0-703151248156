{"branches": ["main", {"name": "develop", "prerelease": "beta", "channel": "beta"}], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/git", {"assets": ["CHANGELOG.md", "package.json"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], "@semantic-release/gitlab", {"assets": [], "successComment": false, "releasedLabels": false}]}