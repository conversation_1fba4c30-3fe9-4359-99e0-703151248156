/**
 * Configuration data for Firebase reset operations
 */

// Constants
const TENANT_ID = 'clove'

// data definitions
const sectionsMetricsData = {
  tenantId: TENANT_ID,
  resto: {
    firstEntry: null,
    totalEntry: 0,
    peakEntry: 0,
    peakTime: null,
  },
  kopitiam: {
    firstEntry: null,
    totalEntry: 0,
    peakEntry: 0,
    peakTime: null,
  },
  elevators: {
    totalCount: 0,
  },
  updatedAt: null,
}

const storeMetricsData = {
  entryStats: {
    last30m: 0,
    overall: 0,
    peak: {
      value: 0,
      time: null,
    },
  },

  tenantId: TENANT_ID,
  updatedAt: null,
}

const queueSummariesData = {
  avgEntryTime: {
    value: 0,
    percentage: null,
  },
  avgServiceTime: {
    value: 0,
  },
  tenantId: TENANT_ID,
  updatedAt: null,
}

const queueMetricsData = {
  entryQueue: {
    avgDwellTime: 0,
    inQueue: 0,
  },
  seatingQueue: {
    avgDwellTime: 0,
    inQueue: 0,
  },
  tenantId: TENANT_ID,
  updatedAt: null,
}

const thresholdsData = {
  tenantId: TENANT_ID,
  avgEntryPercentage: {
    danger: 39,
    warning: 49,
  },
  entryQueue: {
    avgDwellTime: {
      danger: 120,
      warning: 60,
    },
    inQueue: {
      danger: 20,
      warning: 10,
    },
  },
  queueSummary: {
    danger: 120,
    warning: 60,
  },
  queueSummaryEntry: {
    danger: 120,
    warning: 60,
  },
  queueSummaryService: {
    danger: 100,
    warning: 50,
  },
  seatingQueue: {
    avgDwellTime: {
      danger: 120,
      warning: 100,
    },
    inQueue: {
      danger: 120,
      warning: 100,
    },
  },
  targetEntryTime: 120,
  targetServiceTime: 120,
}

const storesData = {
  tenantId: TENANT_ID,
  operatingHours: [
    {
      close: '',
      isClosed: true,
      open: '',
    },
    {
      close: '18:00',
      isClosed: false,
      open: '07:00',
    },
    {
      close: '18:00',
      isClosed: false,
      open: '07:00',
    },
    {
      close: '18:00',
      isClosed: false,
      open: '07:00',
    },
    {
      close: '18:00',
      isClosed: false,
      open: '07:00',
    },
    {
      close: '18:00',
      isClosed: false,
      open: '07:00',
    },
    {
      close: '18:00',
      isClosed: false,
      open: '00:00',
    },
  ],
  timeZone: 'Asia/Singapore',
}

const queueAlertsData = {
  message: '',
  queueCount: 0,
  tenantId: TENANT_ID,
  timestamp: null,
  timestampString: '',
}

module.exports = {
  sectionsMetricsData,
  storeMetricsData,
  queueSummariesData,
  queueMetricsData,
  queueAlertsData,
  storesData,
  thresholdsData,
}
