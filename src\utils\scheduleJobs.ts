import logger from '@/lib/logger'
import * as schedule from 'node-schedule'

/**
 * StrictJobConfig interface defines the configuration for both interval and scheduled jobs.
 *
 * For interval jobs (type: 'interval'):
 * - interval: The interval value (e.g., 10 for every 10 seconds/minutes/hours)
 * - unit: The unit of the interval ('second', 'minute', or 'hour')
 * - startTime: The time to start running the job (hh:mm format)
 * - endTime: The time to stop running the job (hh:mm format)
 * - dayOfWeek: Optional, the day of week to run (0-6, where 0 is Sunday)
 *
 * For scheduled jobs (type: 'scheduled'):
 * - time: The specific time to run the job (hh:mm format)
 * - repeat: The recurrence pattern ('daily', 'weekly', or 'monthly')
 * - dayOfWeek: Required for weekly jobs, the day of week to run (0-6)
 * - dayOfMonth: Required for monthly jobs, the day of month to run (1-31)
 *
 * Common for both:
 * - timezone: IANA timezone string (e.g., 'America/New_York', 'Asia/Tokyo', 'Etc/UTC')
 */
export interface StrictJobConfig {
  name: string
  type: 'interval' | 'scheduled'
  interval: number | null // Interval in seconds, minutes, or hours
  unit: 'second' | 'minute' | 'hour' | null
  time: string | null // For scheduled jobs (hh:mm format)
  repeat: 'daily' | 'weekly' | 'monthly' | null
  dayOfWeek: number | null // 0 (Sunday) – 6 (Saturday)
  dayOfMonth: number | null // 1 – 31
  startTime: string | null // Start time (hh:mm format)
  endTime: string | null // End time (hh:mm format)
  enabled: boolean
  timezone?: string // IANA timezone string (e.g., 'America/New_York', 'Asia/Jakarta')
  handler: () => void
}

/**
 * Validates if an interval job configuration is valid
 *
 * @param config The job configuration to validate
 * @returns boolean indicating if the job config is valid
 */
function isValidIntervalJobConfig(config: StrictJobConfig): boolean {
  return (
    config.type === 'interval' &&
    config.interval !== null &&
    config.unit !== null &&
    config.startTime !== null &&
    config.endTime !== null
  )
}

/**
 * Validates if a scheduled job configuration is valid
 *
 * @param config The job configuration to validate
 * @returns boolean indicating if the job config is valid
 */
function isValidScheduledJobConfig(config: StrictJobConfig): boolean {
  return (
    config.type === 'scheduled' &&
    config.time !== null &&
    config.repeat !== null &&
    // For weekly jobs, dayOfWeek must be provided
    !(config.repeat === 'weekly' && config.dayOfWeek === null) &&
    // For monthly jobs, dayOfMonth must be provided
    !(config.repeat === 'monthly' && config.dayOfMonth === null)
  )
}

/**
 * Creates rules for an interval job that starts at the beginning of the hour
 *
 * @param config Job configuration
 * @param startHour Start hour
 * @param endHour End hour
 * @param timezone Timezone
 * @returns Array of recurrence rules
 */
function createHourlyIntervalRules(
  config: StrictJobConfig,
  startHour: number,
  endHour: number,
  timezone: string
): schedule.RecurrenceRule[] {
  const rules: schedule.RecurrenceRule[] = []
  const { interval, dayOfWeek } = config

  logger.debug(
    `Creating hourly interval rules for job ${config.name} from ${startHour} to ${endHour}`
  )

  // Generate all hours in the range
  const hourRange = []
  for (let h = startHour; h <= endHour; h++) {
    hourRange.push(h)
  }

  // Filter hours based on the interval
  let addedRules = 0
  for (const h of hourRange) {
    // Skip if this hour is outside our range
    if (h < startHour || h > endHour) continue

    // For hourly intervals, only include hours that match the interval pattern
    if (interval && interval > 1) {
      // For the first hour, always include it if startMinute is 0
      const isFirstHour = h === startHour
      const isLastHour = h === endHour

      // Skip hours that don't match the interval pattern, except for the first and last hour
      if (!isFirstHour && !isLastHour && (h - startHour) % interval !== 0) {
        continue
      }
    }

    const rule = new schedule.RecurrenceRule()
    rule.tz = timezone
    rule.hour = h
    rule.minute = 0 // Run at the start of the hour

    // For the start hour, skip if startMinute > 0 (handled by start hour rule)
    if (h === startHour && parseInt(config.startTime!.split(':')[1]) > 0)
      continue

    // For the end hour, skip if endMinute is 0 (nothing to run in that hour)
    if (h === endHour && parseInt(config.endTime!.split(':')[1]) === 0) continue

    if (dayOfWeek !== null) {
      rule.dayOfWeek = dayOfWeek
    }

    rules.push(rule)
    addedRules++
  }

  logger.debug(`Added ${addedRules} hourly rules for job ${config.name}`)

  return rules
}

/**
 * Creates rules for minute or second interval jobs for a specific hour range
 *
 * @param config Job configuration
 * @param unit Unit of interval ('second' or 'minute')
 * @param startHour Start hour
 * @param endHour End hour
 * @param startMinute Start minute
 * @param endMinute End minute
 * @param timezone Timezone
 * @returns Array of recurrence rules
 */
function createMinuteOrSecondIntervalRules(
  config: StrictJobConfig,
  startHour: number,
  endHour: number,
  startMinute: number,
  endMinute: number,
  timezone: string
): schedule.RecurrenceRule[] {
  const rules: schedule.RecurrenceRule[] = []
  const { interval, unit, name, dayOfWeek } = config

  logger.debug(`Creating ${unit} interval rules for job ${name}`)

  // Rule for start hour - only for second and minute intervals
  if (startMinute < 60) {
    const startHourRule = new schedule.RecurrenceRule()
    startHourRule.tz = timezone

    if (unit === 'second') {
      startHourRule.second = new schedule.Range(0, 59, interval!)
      startHourRule.minute = new schedule.Range(startMinute, 59)
    } else if (unit === 'minute') {
      startHourRule.minute = new schedule.Range(startMinute, 59, interval!)
    }

    startHourRule.hour = startHour
    if (dayOfWeek !== null) {
      startHourRule.dayOfWeek = dayOfWeek
    }

    rules.push(startHourRule)
  }

  // Rules for middle hours
  const middleHourCount = endHour - startHour - (startMinute === 0 ? 0 : 1)
  if (middleHourCount > 0) {
    for (let h = startHour + (startMinute === 0 ? 0 : 1); h < endHour; h++) {
      const middleHourRule = new schedule.RecurrenceRule()
      middleHourRule.tz = timezone

      if (unit === 'second') {
        middleHourRule.second = new schedule.Range(0, 59, interval!)
        middleHourRule.minute = new schedule.Range(0, 59)
      } else if (unit === 'minute') {
        middleHourRule.minute = new schedule.Range(0, 59, interval!)
      }

      middleHourRule.hour = h
      if (dayOfWeek !== null) {
        middleHourRule.dayOfWeek = dayOfWeek
      }

      rules.push(middleHourRule)
    }
  }

  // Rule for end hour
  if (endMinute > 0) {
    const endHourRule = new schedule.RecurrenceRule()
    endHourRule.tz = timezone

    if (unit === 'second') {
      endHourRule.second = new schedule.Range(0, 59, interval!)
      endHourRule.minute = new schedule.Range(0, endMinute)
    } else if (unit === 'minute') {
      endHourRule.minute = new schedule.Range(0, endMinute, interval!)
    }

    endHourRule.hour = endHour
    if (dayOfWeek !== null) {
      endHourRule.dayOfWeek = dayOfWeek
    }

    rules.push(endHourRule)
  }

  logger.debug(`Added ${rules.length} ${unit} interval rules for job ${name}`)
  return rules
}

/**
 * Schedules an interval job based on the given configuration
 *
 * @param config The interval job configuration
 * @param timezone The timezone for the job
 * @returns Array of scheduled jobs
 */
function scheduleIntervalJob(
  config: StrictJobConfig,
  timezone: string
): schedule.Job[] {
  const { name, interval, unit, startTime, endTime, dayOfWeek, handler } =
    config

  logger.debug(
    `Setting up interval job "${name}" with ${interval} ${unit} interval`
  )

  const [startHour, startMinute] = startTime!.split(':').map(Number)
  const [endHour, endMinute] = endTime!.split(':').map(Number)

  let rules: schedule.RecurrenceRule[] = []

  if (unit === 'hour') {
    // Hourly intervals are handled differently
    rules = createHourlyIntervalRules(config, startHour, endHour, timezone)
  } else {
    // Second and minute intervals
    rules = createMinuteOrSecondIntervalRules(
      config,
      startHour,
      endHour,
      startMinute,
      endMinute,
      timezone
    )
  }

  // Schedule jobs
  const scheduledJobs: schedule.Job[] = []
  rules.forEach((rule, index) => {
    const job = schedule.scheduleJob(rule, () => {
      handler()
    })
    scheduledJobs.push(job)
  })

  const dayInfo = dayOfWeek !== null ? `on day ${dayOfWeek}` : 'every day'
  logger.info(
    `Scheduled interval job "${name}" with ${interval} ${unit} interval from ${startTime} to ${endTime} ${dayInfo} in timezone ${timezone}.`
  )

  return scheduledJobs
}

/**
 * Schedules a job to run at specific times with recurrence patterns
 *
 * @param config The scheduled job configuration
 * @param timezone The timezone for the job
 * @returns Array of scheduled jobs
 */
function scheduleScheduledJob(
  config: StrictJobConfig,
  timezone: string
): schedule.Job[] {
  const { name, time, repeat, dayOfWeek, dayOfMonth, handler } = config

  logger.debug(`Setting up scheduled job "${name}" with pattern ${repeat}`)

  // Create rule for scheduled job
  const rule = new schedule.RecurrenceRule()
  rule.tz = timezone

  // Parse the time (hh:mm format)
  const [hour, minute] = time!.split(':').map(Number)
  rule.hour = hour
  rule.minute = minute

  // Set the recurrence pattern based on repeat type
  if (repeat === 'daily') {
    // Daily job runs every day at the specified time
    // No additional configuration needed
    logger.debug(`Job ${name} configured for daily execution at ${time}`)
  } else if (repeat === 'weekly') {
    // Weekly job runs on specific day of week
    rule.dayOfWeek = dayOfWeek as number
    logger.debug(
      `Job ${name} configured for weekly execution on day ${dayOfWeek}`
    )
  } else if (repeat === 'monthly') {
    // Monthly job runs on specific day of month
    rule.date = dayOfMonth as number
    logger.debug(
      `Job ${name} configured for monthly execution on day ${dayOfMonth}`
    )
  }

  // Schedule the job
  const job = schedule.scheduleJob(rule, () => {
    handler()
  })

  // Log information about the scheduled job
  let scheduleInfo = ''
  if (repeat === 'daily') {
    scheduleInfo = 'every day'
  } else if (repeat === 'weekly') {
    scheduleInfo = `every week on day ${dayOfWeek}`
  } else if (repeat === 'monthly') {
    scheduleInfo = `every month on day ${dayOfMonth}`
  }

  logger.info(
    `Scheduled job "${name}" to run at ${time} ${scheduleInfo} in timezone ${timezone}.`
  )

  return [job]
}

/**
 * Schedules jobs based on the provided configuration.
 * Supports two types of jobs:
 * 1. Interval jobs: Run at regular intervals within a time range
 * 2. Scheduled jobs: Run at specific times with recurrence patterns
 *
 * @param jobConfig The job configuration
 * @returns A cleanup function that cancels all scheduled jobs
 */
export function scheduleJobs(jobConfig: StrictJobConfig): () => void {
  const {
    name,
    type,
    enabled,
    timezone = Intl.DateTimeFormat().resolvedOptions().timeZone, // Default to machine's timezone if not provided
  } = jobConfig

  // Storage for all scheduled jobs
  const scheduledJobs: schedule.Job[] = []

  if (!enabled) {
    logger.info(`Job "${name}" is disabled.`)
    return () => {}
  }

  logger.debug(`Setting up job "${name}" of type ${type}`)

  // Determine job type and schedule accordingly
  if (isValidIntervalJobConfig(jobConfig)) {
    // Schedule interval job
    const jobs = scheduleIntervalJob(jobConfig, timezone)
    scheduledJobs.push(...jobs)
  } else if (isValidScheduledJobConfig(jobConfig)) {
    // Schedule scheduled job (at specific times)
    const jobs = scheduleScheduledJob(jobConfig, timezone)
    scheduledJobs.push(...jobs)
  } else {
    logger.error(`Invalid configuration for job "${name}".`)
  }

  // Return a function to cancel all scheduled jobs
  return () => {
    scheduledJobs.forEach((job) => job.cancel())
    logger.info(`Cancelled job "${name}"`)
  }
}
