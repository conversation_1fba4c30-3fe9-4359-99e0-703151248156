// Day indices: 0 (Sunday) to 6 (Saturday)
export type DayIndex = 0 | 1 | 2 | 3 | 4 | 5 | 6

// Time string in 24-hour format (HH:MM or H:MM)
// Examples: "09:00", "9:00", "14:30", "23:59"
export type TimeString = string

// Single day's operational hours
export type TOperatingHour = {
  open: string | null
  close: string | null
  isClosed: boolean
}

// Full week map using numeric keys, without timezone for each day
export type TOperatingHours = {
  [key in DayIndex]: TOperatingHour
}

// Full week map with timezone included for each day
export type TOperatingHoursWithTimeZone = {
  [key in DayIndex]: TOperatingHour & { timeZone: string }
}

export type TStore = {
  tenantId: string
  name: string
  timeZone: string
  operatingHours: TOperatingHours
}
