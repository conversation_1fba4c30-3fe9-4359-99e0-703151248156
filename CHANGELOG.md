## [1.6.1](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/compare/v1.6.0...v1.6.1) (2025-05-26)


### Bug Fixes

* add timezone-aware date formatting utilities and ignore testConfig.json ([2b78edb](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/2b78edb25d7806eab2198307060fd90e4f466929))

# [1.6.0](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/compare/v1.5.0...v1.6.0) (2025-05-20)


### Bug Fixes

* update .gitignore to include purgeFireBaseConfig.json ([e08371c](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/e08371c59d4d7633df73cc01827bcbc7e3a5ba98))


### Features

* add Firebase queue alerts purge utility ([88fcb38](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/88fcb3800c33e7a3d1c04d34dbb6a071f2ea3b08))

# [1.5.0](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/compare/v1.4.0...v1.5.0) (2025-05-19)

### Bug Fixes

- **firebase:** improve authentication handling and logging ([81c7dd8](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/81c7dd86b3471ede9b4e16675c255fc729229642))

### Features

- enhance operating hours types to include timezone support and fix shedule ([fae6e1c](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/fae6e1c70df0e5f05c37b3ed12759376d862b533))

# [1.4.0](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/compare/v1.3.0...v1.4.0) (2025-05-16)

### Bug Fixes

- add Firebase key handling to deployment process ([d28e153](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/d28e153821d05e50c1016f98deaa4a52d4599d17))
- enhance application startup and shutdown error handling ([94d1c0b](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/94d1c0b309cea0b409526b59717a9f064594f5be))
- improve Firebase initialization error handling and logging ([789a79b](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/789a79be19ece0a17c17ce661af849dd5a139753))
- improve RabbitMQ error handling and logging ([61f5605](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/61f5605892105d4f95f27b043de86b3f5c69da44))
- validate service account file existence in Firebase config ([28fcfa9](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/28fcfa98e555211064d7a4e1d75c327102b807db))

### Features

- add appError types handler ([831c782](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/831c7828325858284708004565729ba92fff256d))
- integrate Firebase Admin SDK for custom token authentication and update configuration ([c93950c](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/c93950c643c1748b569fc9f582d0c8ab1ec1d886))

# [1.3.0](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/compare/v1.2.1...v1.3.0) (2025-05-09)

### Features

- add QUEUE_ALERT_EXPIRY_MONTHS configuration and update queue alert expire date ([21e599a](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/21e599ad9db35509309f55c6c9a3d42f32675639))

## [1.2.1](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/compare/v1.2.0...v1.2.1) (2025-05-08)

### Bug Fixes

- avg service time calculation to use cached dwell times ([caa0b71](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/caa0b711506a5cc755d06348d6d8ff05a1695e93))
- replace Date with Timestamp and adjust peak time handling ([7933faa](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/7933faa327c694e6b9cd2ac43d155bf77f476091))
- switch string-based date fields to Date or null ([1331b49](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/1331b49da42d1de9b9f4fb32d19cc81ea999f051))
- update timezone format ([cd19421](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/cd19421a2be0d87ee3cbc22ca71f433075c27899))

# [1.2.0](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/compare/v1.1.0...v1.2.0) (2025-05-06)

### Bug Fixes

- enchance job shedule validation ([093e963](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/093e963888336f4ad57522316b193419c5e5a744))
- enchance job shedule validation ([625c583](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/625c58364311a5a33b473909faaab3d02d84a262))
- enhance job scheduling to support cross-midnight intervals ([8c49ec0](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/8c49ec00211e1339b8fbd3d91035b6e15cb2c490))
- enhance job scheduling to support cross-midnight intervals ([1ea577e](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/1ea577ea6b7e2cce4c610cb0f071f7b24651ff51))
- improve code formatting in publishMockEvents function ([e2389b4](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/e2389b4959edac5f1c57af221e32bcb8b22f1a08))
- improve code formatting in publishMockEvents function ([3767783](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/3767783961caae4e134fca954ef6665e9fe58f5b))
- update daily reset job scheduling to account for timezones ([0b88dc3](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/0b88dc3380c61688ba059ae317d6b88fcd7d4773))
- update daily reset job scheduling to account for timezones ([e0ab893](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/e0ab893b642ec4c6d6b76f897265d06b13b5294d))
- update message formatting in mockdata ([753c772](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/753c7725d082d8fa29e2a9b6cb955d97bbe6051c))
- update message formatting in mockdata ([f841aa8](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/f841aa8db55e6492d8171ce7e7f6b8e11b10ed67))

### Features

- Update README test version from 1 to 4 ([315d20b](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/315d20b4eba093bc18d75cd5aa1a80c432281282))
- **utils:** add `getRollingWindowStart()` for standardized window time calculation ([0c1fcc7](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/0c1fcc76806b7078cdeb60dc94873cb258f39a42))

# [1.1.0](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/compare/v1.0.1...v1.1.0) (2025-04-28)

### Bug Fixes

- add RabbitMQ consumer service with message processing and error handling ([a4f05ce](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/a4f05ce4cfc8791e801dee68957ba90a21afb655))
- correct structure of metrics data object in resetData.ts ([e60b85c](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/e60b85cdd341f7fe8c4e01d4d0c1532598ab2394))

### Features

- add Firebase data reset utility script and update .gitignore ([21908ac](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/21908ac4f9b9c0308f52bd62de5f127850739dbf))

## [1.0.1](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/compare/v1.0.0...v1.0.1) (2025-04-23)

### Bug Fixes

- fix server host IP variable in production deployment config ([02cd8b8](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/02cd8b88490636f9921c872af3e15dce730b1bcb))

# 1.0.0 (2025-04-23)

### Bug Fixes

- add database schema and relations for events and temp_queues tables ([3b3a86b](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/3b3a86b6ecbbbb7ebf4fbca395d05a0de4f5d78e))
- **config:** add configuration ([1d79cb7](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/1d79cb7e1007571958b44bf02f687fd0d014de90))
- **config:** change event data ([d7bf619](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/d7bf61996ad1a26a846c0eaf55b3cb7776a2332c))
- **config:** update config to code ([3ae00e1](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/3ae00e193b654bd138dc7d1abfc52b5997eea17f))
- **connection:** share connection at store-metrics ([3bd606d](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/3bd606d384378c749cb58d99dea41d57a3f61347))
- enable versioning stage in GitLab CI pipeline ([5c62e5a](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/5c62e5af785ac371254f6e20d57f6688076db595))
- **event:** add try catch block ([d822189](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/d822189c207511588d5ffc3e295471262cb40b4f))
- **loc.json:** remove ([a4947c1](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/a4947c1086a6c14092a740eeb3b57d3a21d4f2dc))
- **section-metrics:** resolve TPeakData type safety issues ([163cd53](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/163cd532f3e4935dc3f2d24d624b6978cccd9f4e))
- typo in staging env var and add PG cert config for prod deployment ([d33fdd6](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/d33fdd6d73664edaf7c4dac622b4bf82ac1756e4))

### Features

- add daily reset time configuration and implement daily reset job for metrics ([0fce9ae](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/0fce9ae8629e15dd75df86478aed65838dfeabc1))
- add husky preparation script and update daily reset job logging ([c2a30a8](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/c2a30a8c6d1fa567c5ea19c9955408ab018280fc))
- Add new enum and update related code ([7a25fe0](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/7a25fe0311811774a4c2e88e17ebc613cbabaa2e))
- add operating hour buffer configuration and update related logic in Firebase metrics ([a8514fa](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/a8514fa6f92e2e47406c8db65acdd2a93e92e206))
- add PostgreSQL SSL config and update PM2 deployment settings ([a5f1b32](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/a5f1b32130ec6a7d3c230ed658cc63630ade2392))
- add RabbitMQ consumer and job scheduling for metrics processing ([f57d542](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/f57d542432ffabc80b19c545690ed7c9e1d1f593))
- Add RabbitMQ receiver, mock data, and database configuration ([576f57f](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/576f57f1d9c6238a6a1f8e4aa5625ea46009dd51))
- Add skip validation on RabbitMQ messages and enhance mock data ([2ea9631](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/2ea9631ed2ddc09398cb3e038470c715453972f7))
- adjust the new data Structure ([f8c8e7d](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/f8c8e7d15c2d2541c3c325996de53cfa62a4e04f))
- **config:** add debounce configuration settings ([1fb90ca](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/1fb90cacf5e89418700d787a5f62a19c34f5625d))
- **config:** add recent entries time window configuration ([413461d](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/413461d48d8b0a15f4e69274082cb44d46bf1087))
- **env:** add debounce environment variables ([fecad8c](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/fecad8cc766b3901bd67b3e1ee8e561eb6f43673))
- **helper:** make utill functions ([4fdde61](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/4fdde615f00d95d5352b46ea8711bf8a63d4a83a))
- implement CI/CD pipeline with semantic versioning and auto-deployment ([55c0cec](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/55c0cecebbff4dc2b542e8dae30dff8a86f3275e))
- implement cron job scheduling based on operational hours for Firebase metrics updates ([1ef941c](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/1ef941c3305aee81a82d4f9624b18c4cfc3704c5))
- implement getOperatingHours function to adjust operational hours with buffer times ([c7e43b0](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/c7e43b0186d265bcb530d9452bd7d9c687bc9ab2))
- integrate Firebase for metrics updates and add new types for queue and store metrics ([641d479](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/641d479179fd7d1cc37e4aa7352d4179e42664ae))
- **metrics:** implement configurable recent entries tracking ([49b504e](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/49b504e9564acb70d95975aef6eda17927e1cf3c))
- **metrics:** implement peak slots calculation query ([f8b4bc1](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/f8b4bc1239f104726186fabd6dd72c4c45dd3104))
- **metrics:** implement peak slots calculation query ([782b630](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/782b6303c1492b3ce40768fe6552226de9d7c671))
- **metrics:** implement section metrics document service ([f53a3b6](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/f53a3b613d203ffac3e3c6bc37a7bd83238e00a0))
- **metrics:** implement section metrics event handler ([9c2ddf9](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/9c2ddf95d454f7b61a5cad686f33a17912a70523))
- mockdata update end event ([16a7578](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/16a75781d033323103403a40757ad0de2be1b4a6))
- **rabbitmq:** add queueStats feature with robust handling ([25b097f](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/25b097fb565b6c94a32d4a731ef60fbff7e92eb2))
- refactor metrics reset scripts and add Firebase integration ([1652d6a](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/1652d6ae5b73885e573242095bc91f955df5787a))
- remove operational hours check from Firebase metrics update functions ([13143f9](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/13143f9d5a55e7b8fdd2ebd7ecc7a4bbfe277bd1))
- **scripts:** add events table truncation utility ([ae0906c](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/ae0906c1584472486cbfd0101357f1e20fe188fd))
- **scripts:** add section metrics reset utility ([379caad](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/379caadc5792f265f615c7d9de7ea9604e074eea))
- **store-metrics:** implement event-driven metrics with functional approach ([714e317](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/714e3175dad9cc311514705a92c0d41a9fbaa08e))
- **types:** add section metrics type definitions ([53ab4af](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/53ab4afe65b0f3026515f11bea25e12bae2f644f))
- update metrics update intervals from milliseconds to seconds in configuration ([7400ad5](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/7400ad5a2a63f2dc62cef97dff62fe9fe4522411))
- **utils:** add date formatting and timezone utilities ([aafc9fe](https://gitlab.hcorp.io/enfusion/crowd-monitoring-be/commit/aafc9feab648663eb10723436baa0df716476a1a))
