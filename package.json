{"name": "crowd-monitoring-backend", "version": "1.0.0", "description": "Backend for crowd monitoring application", "main": "dist/index.js", "scripts": {"start": "node dist/src/index.js", "dev": "tsx watch src/index.ts", "generate": "npx drizzle-kit generate", "migrate": "npx drizzle-kit migrate", "push": "npx drizzle-kit push", "studio": "npx drizzle-kit studio", "build": "tsc -p tsconfig.build.json && tsc-alias -p tsconfig.build.json", "watch-ts": "tsc -w", "type-check": "tsc --noEmit", "lint": "npx eslint .", "format:fix": "npx prettier --write .", "format:check": "npx prettier --check .", "pm2:start:prod": "pm2 start ecosystem.config.js --env production", "pm2:start:staging": "pm2 start ecosystem.config.js --env staging", "reset:firebase": "node tools/resetFirebaseData.js", "create:firebase": "node tools/createFirebaseData.js", "truncate:events": "tsx scripts/truncateEvents.ts", "truncate:tempQueue": "tsx scripts/truncateTempQueues.ts"}, "dependencies": {"@date-fns/tz": "^1.2.0", "amqplib": "0.10.5", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.0.3", "drizzle-orm": "^0.41.0", "express": "^4.18.2", "firebase": "^11.6.0", "firebase-admin": "^13.4.0", "helmet": "^6.0.1", "lodash": "^4.17.21", "morgan": "^1.10.0", "node-schedule": "^2.1.1", "pg": "^8.14.1", "qrcode-terminal": "^0.12.0", "uuid": "^11.1.0", "whatsapp-web.js": "^1.27.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.2"}, "_comment": "Types amqplib and amqplib use exact version https://stackoverflow.com/questions/79519639/typescript-error-when-using-amqplib-that-cant", "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/js": "^9.25.1", "@types/amqplib": "0.10.6", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/lodash": "^4.17.16", "@types/morgan": "^1.9.4", "@types/node": "^18.15.3", "@types/node-schedule": "^2.1.7", "@types/pg": "^8.11.11", "@types/qrcode-terminal": "^0.12.2", "cross-env": "^7.0.3", "drizzle-kit": "^0.30.6", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.2", "globals": "^16.0.0", "husky": "^9.1.7", "pm2": "^5.3.1", "prettier": "^3.2.5", "tsc-alias": "^1.8.11", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.3", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0"}}