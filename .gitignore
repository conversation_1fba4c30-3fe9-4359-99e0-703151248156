# Dependencies
node_modules
.pnp
.pnp.js

# Build outputs
dist
build
out
.next

# Environment
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production
.env.staging
.env.staging.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
coverage
.nyc_output

# IDEs and editors
.idea
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS
.DS_Store
Thumbs.db

# Expo/React Native
.expo
.expo-shared
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*
web-build/

# Temporary files
*.tmp
*.temp

.windsurfrules
# whatsApp JS auth stategy
.wwebjs_*

certs

resetFireBaseConfig.json
createFireBaseConfig.json
serviceAccountKey.json
purgeFireBaseConfig.json
testConfig.json
