import logger from '@/lib/logger'
import { dbFirebase } from '@/firebase/dbFirebase'
import {
  doc,
  DocumentReference,
  updateDoc,
  increment,
} from 'firebase/firestore'
import { config } from '@/config'
import { fireBaseCollection } from '@/config'

const docRef: DocumentReference = doc(
  dbFirebase,
  fireBaseCollection.STORE_METRICS,
  config.tenantId
)

/**
 * Updates the overall entry count in Firestore using atomic increment.
 * @param incrementValue The value to increment the overall entry count by.
 * @returns Promise<void>
 */
const updateOverallEntry = async (incrementValue: number): Promise<void> => {
  try {
    const updateData = {
      'entryStats.overall': increment(incrementValue),
      tenantId: config.tenantId,
      updatedAt: new Date(),
    }

    await updateMetricsDocument(updateData)

    logger.info('Overall entry updated in Firestore', { updateData })
  } catch (err) {
    logger.error('Error updating overall entry in Firestore:', { error: err })
    throw err // Re-throw to allow caller to handle the error if needed
  }
}

/**
 * Updates the peak queue value and timestamp in Firestore.
 * @param peakValue The peak queue count to set.
 * @param peakTime The timestamp of the peak queue count.
 * @returns Promise<void>
 */
const updatePeakQueue = async (
  peakValue: number,
  peakTime: Date | null
): Promise<void> => {
  try {
    const updateData = {
      'entryStats.peak.value': peakValue,
      'entryStats.peak.time': peakTime,
    }

    await updateMetricsDocument(updateData)

    logger.info('Peak queue updated in Firestore', { updateData })
  } catch (err) {
    logger.error('Error updating peak queue in Firestore:', { error: err })
    throw err // Re-throw to allow caller to handle the error if needed
  }
}

/**
 * Shared function to perform Firestore updates.
 * @param updates Record of fields to update in the document.
 * @returns Promise<void>
 */
const updateMetricsDocument = async (
  updates: Record<string, any>
): Promise<void> => {
  try {
    await updateDoc(docRef, updates)
  } catch (err) {
    logger.error('Error updating Firestore document:', { error: err })
    throw err
  }
}

// Export shared utilities
export { docRef, updateOverallEntry, updatePeakQueue, updateMetricsDocument }
