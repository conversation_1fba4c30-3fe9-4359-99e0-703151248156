import {
  pgTable,
  index,
  uuid,
  text,
  timestamp,
  integer,
} from 'drizzle-orm/pg-core'

export const tempQueues = pgTable(
  'temp_queues',
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    evtType: text('evt_type').notNull(),
    cam: text().notNull(),
    ts: timestamp({ withTimezone: true, mode: 'string' }).notNull(),
    tId: text('t_id'),
    qName: text('q_name'),
    loc: text().notNull(),
    dur: integer().notNull(),
    count: integer().notNull(),
    tenantId: text('tenant_id').notNull(),
    createdAt: timestamp('created_at', {
      withTimezone: true,
      mode: 'string',
    }).defaultNow(),
  },
  (table) => [
    index('ts_idx').using(
      'btree',
      table.ts.asc().nullsLast().op('timestamptz_ops')
    ),
  ]
)

export const events = pgTable('events', {
  id: uuid().defaultRandom().primaryKey().notNull(),
  evtType: text('evt_type').notNull(),
  cam: text().notNull(),
  ts: timestamp({ withTimezone: true, mode: 'string' }).notNull(),
  tId: text('t_id'),
  qName: text('q_name'),
  loc: text().notNull(),
  dur: integer().notNull(),
  count: integer().notNull(),
  tenantId: text('tenant_id').notNull(),
  createdAt: timestamp('created_at', {
    withTimezone: true,
    mode: 'string',
  }).defaultNow(),
})
