import path from 'path'
import * as winston from 'winston'
import DailyRotateFile from 'winston-daily-rotate-file'

const logPath: string = path.resolve(`../logs/`)

const commonFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    const metaString = Object.keys(meta).length
      ? JSON.stringify(meta, null, 2)
      : ''
    return `${timestamp} [${level}]: ${message} ${metaString}`
  })
)

const fileFormat = winston.format.combine(
  winston.format.splat(),
  winston.format.uncolorize({ level: true }),
  commonFormat
)

const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  commonFormat
)

const configTransportCombined: DailyRotateFile = new DailyRotateFile({
  filename: '%DATE%.log',
  dirname: path.join(logPath, 'combined'),
  datePattern: 'YYYY-MM-DD',
  zippedArchive: true,
  maxSize: '200m',
  maxFiles: '7d',
  level: 'debug',
  format: fileFormat,
})

const configTransportError: DailyRotateFile = new DailyRotateFile({
  filename: '%DATE%.log',
  dirname: path.join(logPath, 'error'),
  datePattern: 'YYYY-MM-DD',
  zippedArchive: true,
  maxSize: '200m',
  maxFiles: '7d',
  level: 'error',
  format: fileFormat,
})

// Create the logger
const logger = winston.createLogger({
  transports: [
    configTransportCombined,
    configTransportError,
    new winston.transports.Console({
      level: 'debug',
      format: consoleFormat,
    }),
  ],
})

export default logger
