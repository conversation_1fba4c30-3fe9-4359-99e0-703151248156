import { config, fireBaseCollection } from '@/config'
import { dbFirebase } from '@/firebase/dbFirebase'
import logger from '@/lib/logger'
import { TSectionsMetrics } from '@/services/section-metrics/types'
import { doc, DocumentReference, getDoc, updateDoc } from 'firebase/firestore'

// Shared in-memory state
const isDocumentInitialized: boolean = true
// Cache for firstEntry values
const firstEntryCache: { kopitiam: Date | null; resto: Date | null } = {
  resto: null,
  kopitiam: null,
}

// Shared document reference
const docRef: DocumentReference = doc(
  dbFirebase,
  fireBaseCollection.SECTIONS_METRICS,
  config.tenantId
)

// Ensure the document exists and initialize if it doesn't
// Return the current document data if needed (e.g., to populate the cache)
const ensureDocumentExists = async (): Promise<TSectionsMetrics | null> => {
  try {
    if (isDocumentInitialized) {
      // Document is already initialized, return the cached firstEntry values
      return {
        tenantId: config.tenantId,
        resto: {
          firstEntry: firstEntryCache.resto,
          totalEntry: 0,
          peakEntry: 0,
          peakTime: null,
        },
        kopitiam: {
          firstEntry: firstEntryCache.kopitiam,
          totalEntry: 0,
          peakEntry: 0,
          peakTime: null,
        },
        elevators: {
          totalCount: 0,
        },
        updatedAt: null,
      }
    }

    // Check if the document exists
    const docSnapshot = await getDoc(docRef)

    if (!docSnapshot.exists()) {
      logger.warn('Sections metrics document not found, skipping processing')
      return null
    }

    const currentData = docSnapshot.data() as TSectionsMetrics
    logger.info('Fetched sectionsMetrics from Firestore')

    // Populate the firstEntry cache
    firstEntryCache.kopitiam = currentData.kopitiam.firstEntry
    firstEntryCache.resto = currentData.resto.firstEntry

    return currentData
  } catch (err) {
    logger.error('Error ensuring document exists:', { error: err })
    throw err
  }
}

// Shared function to perform Firestore updates
const updateMetricsDocument = async (
  updates: Record<string, any>
): Promise<void> => {
  try {
    await updateDoc(docRef, updates)
    // logger.info('Updated Firestore document:', { updates })
  } catch (err) {
    logger.error('Error updating Firestore document:', { error: err })
    throw err
  }
}

// Export shared utilities
export { docRef, ensureDocumentExists, firstEntryCache, updateMetricsDocument }
