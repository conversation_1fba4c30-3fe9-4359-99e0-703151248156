import { dbFirebase } from '@/firebase/dbFirebase'
import logger from '@/lib/logger'
import { doc, setDoc } from 'firebase/firestore'
import { getWhatsAppService } from './whatsapp.service'
import { config } from '@/config'

let lastAlertedThreshold = 0

export async function checkQueueThresholds(q1Count: number): Promise<void> {
  const multiplier = config.alert.thresholdMultiplier

  try {
    const thresholdMultiple = Math.floor(q1Count / multiplier) * multiplier

    // Upward crossing: send alert only once per threshold
    if (q1Count >= multiplier && thresholdMultiple > lastAlertedThreshold) {
      const alertMessage = `Queue Above ${thresholdMultiple}`
      await sendQueueAlert(alertMessage, q1Count)
      logger.info(`Queue threshold exceeded: ${alertMessage}`, {
        queueCount: q1Count,
      })
      lastAlertedThreshold = thresholdMultiple
    }

    // Downward crossing: send alert only once per threshold drop
    if (q1Count < lastAlertedThreshold && lastAlertedThreshold >= multiplier) {
      const alertMessage = `Queue Dropped Below ${lastAlertedThreshold}`
      await sendQueueAlert(alertMessage, q1Count)
      logger.info(`Queue threshold dropped below: ${alertMessage}`, {
        queueCount: q1Count,
      })
      lastAlertedThreshold = Math.floor(q1Count / multiplier) * multiplier
    }
  } catch (error) {
    logger.error('Error checking queue thresholds:', error)
  }
}

// Function to send queue alerts to Firebase and whatsApp
async function sendQueueAlert(
  alertMessage: string,
  queueCount: number
): Promise<void> {
  try {
    const now = new Date()
    const expireDate = new Date(now)
    expireDate.setMonth(now.getMonth() + config.alert.expiryMonths)

    const alertData = {
      message: alertMessage,
      queueCount,
      timestampString: now.toISOString(),
      timestamp: now,
      expireAt: expireDate,
      tenantId: 'clove',
    }

    const alertRef = doc(dbFirebase, 'queueAlerts', `alert_${Date.now()}`)
    await setDoc(alertRef, alertData)

    if (config.whatsapp.enabled) {
      await getWhatsAppService().sendQueueAlert(alertMessage)
    }

    logger.info('Queue alert sent:', alertData)
  } catch (error) {
    logger.error('Error sending queue alert:', error)
  }
}
