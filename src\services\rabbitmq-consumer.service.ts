import amqp, { Channel, Connection, Message } from 'amqplib'
import { config } from '@/config'
import logger from '@/lib/logger'
import { db } from '@/database/db'
import { events, tempQueues } from '@/database/schema'
import { handleSectionEvent } from './section-metrics/handlers/count-event.handler'
import { handleStoreEvent } from './store-metrics/handlers/store-event.handler'
import { handleQueueEvent } from './queue-metrics'
import {
  Camera,
  EventData,
  EventType,
  Location,
  QueueName,
} from '@/types/eventData.types'

export class RabbitMQReceiver {
  private readonly QUEUE_NAME = config.rabbitmq.queueName
  private readonly INITIAL_DELAY_MS = config.rabbitmq.initialRetryDelayMs
  private readonly MAX_DELAY_MS = config.rabbitmq.maxRetryDelayMs
  private connection: Connection | null = null
  private channel: Channel | null = null
  private messageCount = 0
  private isReconnecting = false
  private reconnectAttempts = 0
  private shutdownPromise: Promise<void> | null = null

  private async sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  private async connectWithRetry(throwOnError: boolean = false): Promise<void> {
    try {
      logger.info(
        `Attempting to connect to RabbitMQ with VHOST: ${config.rabbitmq.vhost}`
      )

      this.connection = await amqp.connect({
        protocol: config.rabbitmq.protocol,
        hostname: config.rabbitmq.hostname,
        port: config.rabbitmq.port,
        username: config.rabbitmq.username,
        password: config.rabbitmq.password,
        vhost: config.rabbitmq.vhost,
        heartbeat: config.rabbitmq.heartbeat,
      })

      this.connection.on('error', (err) => {
        if (!this.shutdownPromise) {
          logger.error('Connection error', { error: err.message })
          this.scheduleReconnect()
        }
      })

      this.connection.on('close', () => {
        if (!this.shutdownPromise) {
          logger.error('Connection closed')
          this.scheduleReconnect()
        }
      })

      this.channel = await this.connection.createChannel()
      await this.channel.assertQueue(this.QUEUE_NAME, { durable: true })
      await this.channel.prefetch(1)

      logger.info(`Waiting for messages in ${this.QUEUE_NAME}.`)
      this.setupConsumer()

      this.reconnectAttempts = 0
      this.isReconnecting = false
    } catch (error) {
      logger.error('Failed to connect to RabbitMQ', {
        error:
          error instanceof Error
            ? { message: error.message, stack: error.stack }
            : String(error),
      })
      if (throwOnError) {
        // Rethrow the error if we're in startup mode
        throw error
      } else {
        // Otherwise, schedule reconnection
        await this.scheduleReconnect()
      }
    }
  }

  private async scheduleReconnect(): Promise<void> {
    if (this.isReconnecting) return

    this.isReconnecting = true
    const delay = Math.min(
      this.MAX_DELAY_MS,
      this.INITIAL_DELAY_MS * Math.pow(2, this.reconnectAttempts)
    )

    logger.info(`Reconnecting in ${delay / 1000} seconds...`)
    await this.sleep(delay)
    this.reconnectAttempts++

    this.isReconnecting = false
    await this.connectWithRetry()
  }

  private setupConsumer(): void {
    if (!this.channel) return

    this.channel.consume(this.QUEUE_NAME, (msg) => this.processMessage(msg), {
      noAck: false,
    })
  }

  private async processMessage(msg: Message | null): Promise<void> {
    if (!msg || !this.channel) {
      logger.error('No message or channel available')
      return
    }

    this.messageCount++

    try {
      // Step 1: Decode the Buffer to a UTF-8 string
      const jsonString = msg.content.toString('utf8')

      // Step 2: Parse the JSON string to a JavaScript object
      const messageContent = JSON.parse(jsonString)

      const [evt_type, cam, ts, t_id, q_name, loc, dur, count, tenant_id] =
        messageContent.split(',')

      const event_data: EventData = {
        evt_type: evt_type as EventType,
        cam: cam as Camera,
        ts: new Date(ts),
        t_id: t_id === '' ? null : t_id,
        q_name: q_name === '' ? null : (q_name as QueueName | null),
        loc: loc as Location,
        dur: parseInt(dur, 10),
        count: parseInt(count, 10),
        tenant_id,
      }

      // logger.info(`Message ${this.messageCount} received`, {
      //   content: messageContent,
      // })

      // Insert into the database using Drizzle ORM
      await db.insert(events).values(event_data)
      if (event_data.evt_type.startsWith('q_')) {
        await db.insert(tempQueues).values(event_data)
      }
      // logger.info(`Message ${this.messageCount} inserted into database`, {
      //   event_data,
      // })

      await handleSectionEvent(event_data)
      await handleStoreEvent(event_data)
      await handleQueueEvent(event_data)

      // Acknowledge the message
      this.channel.ack(msg)
    } catch (error) {
      logger.error(`Message ${this.messageCount} failed to parse`, {
        error: error instanceof Error ? error.message : String(error),
        content: msg.content.toString(),
      })
      this.channel.nack(msg, false, false)
      return
    }
  }

  private async gracefulShutdown(): Promise<void> {
    logger.info('Initiating graceful shutdown...')
    logger.info(`Total messages received: ${this.messageCount}`)

    try {
      if (this.channel) {
        await this.channel.close()
        logger.info('RabbitMQ channel closed.')
      }
      if (this.connection) {
        await this.connection.close()
        logger.info('RabbitMQ connection closed.')
      }
      logger.info('Graceful shutdown rabbmitmq completed.')
    } catch (error) {
      logger.error('Error during graceful shutdown', {
        error: error instanceof Error ? error.message : String(error),
      })
    }
  }

  public async start(throwOnError: boolean = false): Promise<void> {
    try {
      await this.connectWithRetry(throwOnError)
    } catch (error) {
      // Only rethrow if throwOnError is true
      if (throwOnError) throw error
    }
  }

  public async stop(): Promise<void> {
    if (this.shutdownPromise) {
      return this.shutdownPromise
    }

    this.shutdownPromise = this.gracefulShutdown()
  }
}
