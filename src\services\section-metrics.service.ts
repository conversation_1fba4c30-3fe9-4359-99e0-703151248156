import { dbFirebase } from '@/firebase/dbFirebase'
import logger from '@/lib/logger'

import { db } from '@/database/db'
import { events } from '@/database/schema'
import { and, eq, gte, sql } from 'drizzle-orm'
import { doc, setDoc } from 'firebase/firestore'
import { TSectionsMetrics } from '@/types/sectionsMetrics.types'

// Local cache to store the latest sectionsMetrics data
let cachedSectionsMetrics: TSectionsMetrics | null = null

export const updateSectionsMetricsFirebase = async () => {
  const elevExit = await getElevatorsExit()
  const kopiEntryStats = await getKopitiamStats()
  const restoEntryStats = await getRestoStats()

  const newSectionsMetrics: TSectionsMetrics = {
    resto: {
      firstEntry: restoEntryStats.firstEntry
        ? new Date(restoEntryStats.firstEntry).toISOString()
        : '',
      totalEntry: restoEntryStats.count,
      peakEntry: restoEntryStats.peakCount,
      peakTime: restoEntryStats.peakTime
        ? new Date(restoEntryStats.peakTime).toISOString()
        : '', // or use null if you update the type
    },
    kopitiam: {
      firstEntry: kopiEntryStats.firstEntry
        ? new Date(kopiEntryStats.firstEntry).toISOString()
        : '',
      totalEntry: kopiEntryStats.count,
      peakEntry: kopiEntryStats.peakCount,
      peakTime: restoEntryStats.peakTime
        ? new Date(restoEntryStats.peakTime).toISOString()
        : '', // or use null if you update the type
    },
    elevators: {
      totalCount: elevExit,
    },
    tenantId: 'clove',
    updatedAt: new Date(),
  }

  // Compare with local cache
  if (
    cachedSectionsMetrics &&
    cachedSectionsMetrics.resto.totalEntry ===
      newSectionsMetrics.resto.totalEntry &&
    cachedSectionsMetrics.resto.peakEntry ===
      newSectionsMetrics.resto.peakEntry &&
    cachedSectionsMetrics.kopitiam.totalEntry ===
      newSectionsMetrics.kopitiam.totalEntry &&
    cachedSectionsMetrics.kopitiam.peakEntry ===
      newSectionsMetrics.kopitiam.peakEntry &&
    cachedSectionsMetrics.elevators.totalCount ===
      newSectionsMetrics.elevators.totalCount
  ) {
    logger.debug('No changes in sections metrics, skipping update.')
    return
  }

  // Update Firebase and local cache
  logger.info('Updating sections metrics', {
    sectionsMetrics: newSectionsMetrics,
  })
  const updateResult = await updateSectionsMetrics(newSectionsMetrics)

  if (updateResult.success) {
    cachedSectionsMetrics = newSectionsMetrics // Save to local cache
  }
}

async function getElevatorsExit() {
  const startOfDay = new Date(new Date().setHours(0, 0, 0, 0))

  const result = await db
    .select({
      count: sql<string>`count(*)`.as('elev_exit_count'),
    })
    .from(events)
    .where(and(gte(events.ts, startOfDay), eq(events.evt_type, 'elev_exit')))

  return parseInt(result[0]?.count ?? '0', 10)
}

async function getKopitiamStats(intervalMinutes = 30) {
  const startOfDay = new Date(new Date().setHours(0, 0, 0, 0))

  const result = await db.execute(sql`
            WITH time_slots AS (
                SELECT
                    date_trunc('hour', ${events.ts}) +
                    (INTERVAL '1 minute' * (${intervalMinutes} * (EXTRACT(minute FROM ${events.ts})::integer / ${intervalMinutes}))) AS slot_time,
                    COUNT(*) AS entry_count
                FROM ${events}
                WHERE
                    ${events.ts} >= ${startOfDay}
                    AND ${events.evt_type} = 'kopi_entry'
                GROUP BY slot_time
            ),
            peak_slot AS (
                SELECT
                    slot_time AS peak_time,
                    entry_count AS peak_count
                FROM time_slots
                ORDER BY entry_count DESC
                LIMIT 1
            )
            SELECT
                (SELECT COUNT(*) FROM ${events}
                 WHERE ${events.ts} >= ${startOfDay}
                 AND ${events.evt_type} = 'kopi_entry') AS total_count,
                (SELECT MIN(${events.ts}) FROM ${events}
                 WHERE ${events.ts} >= ${startOfDay}
                 AND ${events.evt_type} = 'kopi_entry') AS first_entry,
                peak_time,
                peak_count
            FROM peak_slot
        `)

  if (result.rowCount === 0) {
    return {
      count: 0,
      firstEntry: null,
      peakTime: null,
      peakCount: 0,
    }
  }

  return {
    count: parseInt((result.rows[0].total_count as string) ?? '0'),
    firstEntry: (result.rows[0].first_entry as Date) ?? null,
    peakTime: (result.rows[0].peak_time as Date) ?? null,
    peakCount: parseInt((result.rows[0].peak_count as string) ?? '0'),
  }
}

async function getRestoStats(intervalMinutes = 30) {
  const startOfDay = new Date(new Date().setHours(0, 0, 0, 0))

  const result = await db.execute(sql`
            WITH time_slots AS (
                SELECT
                    date_trunc('hour', ${events.ts}) +
                    (INTERVAL '1 minute' * (${intervalMinutes} * (EXTRACT(minute FROM ${events.ts})::integer / ${intervalMinutes}))) AS slot_time,
                    COUNT(*) AS entry_count
                FROM ${events}
                WHERE
                    ${events.ts} >= ${startOfDay}
                    AND ${events.evt_type} IN ('q_exit_resto', 'resto_entry')
                GROUP BY slot_time
            ),
            peak_slot AS (
                SELECT
                    slot_time AS peak_time,
                    entry_count AS peak_count
                FROM time_slots
                ORDER BY entry_count DESC
                LIMIT 1
            )
            SELECT
                (SELECT COUNT(*) FROM ${events}
                 WHERE ${events.ts} >= ${startOfDay}
                 AND ${events.evt_type} IN ('q_exit_resto', 'resto_entry')) AS total_count,
                (SELECT MIN(${events.ts}) FROM ${events}
                 WHERE ${events.ts} >= ${startOfDay}
                 AND ${events.evt_type} IN ('q_exit_resto', 'resto_entry')) AS first_entry,
                peak_time,
                peak_count
            FROM peak_slot
        `)

  if (result.rowCount === 0) {
    return {
      count: 0,
      firstEntry: null,
      peakTime: null,
      peakCount: 0,
    }
  }

  return {
    count: parseInt((result.rows[0].total_count as string) ?? '0'),
    firstEntry: (result.rows[0].first_entry as Date) ?? null,
    peakTime: (result.rows[0].peak_time as Date) ?? null,
    peakCount: parseInt((result.rows[0].peak_count as string) ?? '0'),
  }
}

export const updateSectionsMetrics = async (
  metricsData: TSectionsMetrics,
  documentId: string = 'clove'
): Promise<{
  success: boolean
  error: string | null
}> => {
  try {
    const metricsRef = doc(dbFirebase, 'sectionsMetrics', documentId)

    await setDoc(metricsRef, metricsData, { merge: true })

    return {
      success: true,
      error: null,
    }
  } catch (error) {
    logger.error('Error updating sections metrics:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    }
  }
}
