import { config } from '@/config'
import logger from '@/lib/logger'
import { StrictJobConfig, scheduleJobs } from '@/utils/scheduleJobs'
import { TOperatingHoursWithTimeZone } from '@/types/store.types'
import { getOperatingHours } from '@/services/update-firebase.service'
import { updateRecentEntries } from '@/services/store-metrics/queries/recent-entries.query'
import { updateAverageDwellTime } from '@/services/queue-metrics/queries/dwell-time.query'
import { updateQueueSummaryFirebase } from '@/services/queue-summary.service'
import { processPeak } from '@/services/section-metrics/handlers/process-peak.handler'
import { resetAllMetricsFirebase } from '@/services/reset-firebase-metrics.service'
import { truncateTempQueues } from '@/services/truncate-temp-queues.service'
import { TZDate } from '@date-fns/tz'

// Array to store cleanup functions for all scheduled jobs
let jobsCleanupFunctions: (() => void)[] = []

/**
 * Creates job configurations based on operating hours
 *
 * @param operatingHours The operating hours with timezone information
 * @returns Array of job configurations
 */
export function createJobConfigsFromOperatingHours(
  operatingHours: TOperatingHoursWithTimeZone | null
): StrictJobConfig[] {
  if (!operatingHours) return []

  const configs: StrictJobConfig[] = []

  // Create a config for each job
  const jobDefinitions = [
    {
      name: 'Store Recent Entries',
      handler: updateRecentEntries,
      interval: config.firebaseCronUpdate.storeMetrics,
    },
    {
      name: 'Average Dwell Time Metrics',
      handler: updateAverageDwellTime,
      interval: config.firebaseCronUpdate.queueMetrics,
    },
    {
      name: 'Queue Summary',
      handler: updateQueueSummaryFirebase,
      interval: config.firebaseCronUpdate.queueSummary,
    },
    {
      name: 'Sections Metrics',
      handler: processPeak,
      interval: config.firebaseCronUpdate.sectionsMetrics,
    },
  ]

  // For each day with operating hours
  Object.entries(operatingHours).forEach(([dayIndex, dayHours]) => {
    // Skip if the day is marked as closed
    if (dayHours.isClosed || !dayHours.open || !dayHours.close) return

    // For each job definition
    jobDefinitions.forEach((jobDef) => {
      configs.push({
        name: `${jobDef.name} - Day ${dayIndex}`,
        type: 'interval',
        interval: jobDef.interval,
        unit: 'second',
        time: null,
        repeat: null,
        dayOfWeek: parseInt(dayIndex),
        dayOfMonth: null,
        startTime: dayHours.open,
        endTime: dayHours.close,
        enabled: true,
        timezone: dayHours.timeZone,
        handler: async () => {
          try {
            logger.debug(`Running job ${jobDef.name} for day ${dayIndex}`)
            await Promise.resolve(jobDef.handler())
          } catch (error) {
            logger.error(`Error in ${jobDef.name} job:`, {
              error: error instanceof Error ? error.message : String(error),
            })
          }
        },
      })
    })
  })

  return configs
}

/**
 * Schedules the daily reset job
 *
 * @returns Cleanup function for the scheduled job
 */
export function scheduleDailyResetJob(): () => void {
  const timezone = config.time.timezone
  const resetTime = config.reset.dailyResetTime

  // Parse the reset time
  const [hours, minutes] = resetTime.split(':').map(Number)

  // Get current date
  const today = new Date()

  // Create a date object in the specified timezone with the reset time
  const targetDateInTimezone = TZDate.tz(
    timezone,
    today.getFullYear(),
    today.getMonth(),
    today.getDate(),
    hours,
    minutes,
    0
  )

  // Convert this time to local time
  const localResetDate = new Date(targetDateInTimezone.toISOString())

  // Format the local time as HH:MM for the job scheduler
  const localTimeString = `${String(localResetDate.getHours()).padStart(2, '0')}:${String(
    localResetDate.getMinutes()
  ).padStart(2, '0')}`

  const resetJobConfig: StrictJobConfig = {
    name: 'Daily Metrics Reset',
    type: 'scheduled',
    interval: null,
    unit: null,
    time: localTimeString,
    repeat: 'daily',
    dayOfWeek: null,
    dayOfMonth: null,
    startTime: null,
    endTime: null,
    enabled: true,
    handler: async () => {
      try {
        logger.info(`Starting daily reset jobs at ${resetTime} (${timezone})`)

        // Run resetAllMetrics
        logger.info('Running resetAllMetrics')
        await resetAllMetricsFirebase()
        logger.info('resetAllMetrics completed successfully')

        // Run truncateTempQueues
        logger.info('Running truncateTempQueues')
        await truncateTempQueues()
        logger.info('truncateTempQueues completed successfully')

        logger.info('Daily reset jobs completed successfully')
      } catch (error) {
        logger.error('Error in daily reset job:', {
          error: error instanceof Error ? error.message : String(error),
        })
      }
    },
  }

  const cleanupFn = scheduleJobs(resetJobConfig)

  logger.info(
    `Scheduled daily reset job at ${resetTime} (${timezone}), which is ${localTimeString} in local server time`
  )

  return cleanupFn
}

/**
 * Updates jobs based on operating hours
 * Clears existing jobs and creates new ones
 */
export function updateJobsFromOperatingHours(): void {
  // Clear any existing jobs
  jobsCleanupFunctions.forEach((cleanup) => cleanup())
  jobsCleanupFunctions = []

  const operatingHours = getOperatingHours()

  // Create new job configurations
  const jobConfigs = createJobConfigsFromOperatingHours(operatingHours)

  // Create new jobs
  jobConfigs.forEach((config) => {
    const cleanupFn = scheduleJobs(config)
    jobsCleanupFunctions.push(cleanupFn)
  })

  logger.info('Updated jobs based on new operating hours', {
    totalJobs: jobConfigs.length,
    jobNames: jobConfigs.map((j) => j.name),
  })

  // Schedule the daily reset job
  const resetJobCleanupFn = scheduleDailyResetJob()
  jobsCleanupFunctions.push(resetJobCleanupFn)
}

/**
 * Cleans up all scheduled jobs
 */
export function cleanupAllJobs(): void {
  jobsCleanupFunctions.forEach((cleanup) => cleanup())
  jobsCleanupFunctions = []
  logger.info('All scheduled jobs cleaned up')
}
