#!/bin/bash
set -e

echo "🔄 Merging main → develop (with [skip ci])"

# Ensure latest refs
git fetch origin main
git fetch origin develop

# Checkout develop from remote if not yet available
git checkout -B develop origin/develop

# Merge main into develop
git merge origin/main --no-ff -m "chore(sync): merge main into develop [skip ci]"

# Push the result
git push origin develop

echo "✅ Sync complete"
