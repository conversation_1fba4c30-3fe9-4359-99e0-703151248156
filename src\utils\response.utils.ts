import { Response } from 'express'

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  timestamp: string
}

function createApiResponse<T>(
  success: boolean,
  data?: T,
  error?: string
): ApiResponse<T> {
  return {
    success,
    data,
    error,
    timestamp: new Date().toISOString(),
  }
}

export function sendSuccess<T>(
  res: Response,
  data: T,
  status: number = 200
): void {
  res.status(status).json(createApiResponse(true, data))
}

export function sendError(
  res: Response,
  error: string | Error,
  status: number = 500
): void {
  const errorMessage = error instanceof Error ? error.message : error
  res.status(status).json(createApiResponse(false, undefined, errorMessage))
}
