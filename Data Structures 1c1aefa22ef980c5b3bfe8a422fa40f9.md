# Data Structures

### 🗂️ Field Mapping and Value Types

| Original       | Short name for DB Table field | **Type / Example Values**                                                                                              | Column | Type    |
| -------------- | ----------------------------- | ---------------------------------------------------------------------------------------------------------------------- | ------ | ------- |
| `event_type`   | `evt_type`                    | `"q_entry"`, `"q_exit"`, `"q_exit_resto"`, `"kopi_entry"`, `"elev_exit"` , `resto_entry` , `du_kopi_entry` `q_abandon` | 1      | string  |
| `camera_id`    | `cam`                         | `"c1"`, `"c2"`, `"c3"`, `"c4"`, `"c5"`                                                                                 | 2      | string  |
| `timestamp`    | `ts`                          | **ISO 8601** timestamp in **UTC** e.g., **`2025-03-23T10:15:30.123Z`**                                                 | 3      | date    |
| `tracker_id`   | `t_id`                        | UUID string, e.g., `"123e4567-e89b-12d3-a456-************"`                                                            | 4      | string  |
| `queue_name`   | `q_name`                      | `"q1"`, `"q2"`, or `null` for non-queue events                                                                         | 5      | string  |
| `location`     | `loc`                         | `"elev_1"`, `"elev_2"`, `"q1"`, `"q2"`, `"kopi"`, `"resto"`                                                            | 6      | string  |
| `duration`     | `dur`                         | Number (seconds, integer only), e.g., `0`, `4`, `120`                                                                  | 7      | integer |
| `curren_count` | `count`                       | Number (seconds, integer only), e.g 0, 4, 120                                                                          | 8      | integer |
|                |                               |                                                                                                                        |        |         |
|                |                               |                                                                                                                        |        |         |

**Benefit**: Reduces JSON payload size which improves serialization/de-serialization performance and reduces network overhead.

### ✅ Data Structure

Each event generated by a camera has the following fields:

- **evt_type**: The type of event (e.g., `"q_entry"`, `"q_exit"`, `"q_exit_resto"`, `resto_entry`, `"kopi_entry"`, `"elev_exit`, `resto_entry`, ).
- **cam**: The camera ID (e.g., `"c1"`, `"c2"`, `"c3"`, `"c4"`, `"c5"`).
- **ts**: The timestamp of the event in **ISO 8601** timestamp in **UTC ( `2025-03-23T10:15:30.123Z` )**
- **t_id**: A unique tracker ID for each person created by each camera. (only`c4` and `c5` sharing tracker id when joining Queue_01).
- **q_name**: The queue ID (e.g., `"q1"`, `"q2"`, or `null` for non-queue events like `"kopi_entry"`).
- **loc**: The location (e.g., `"elev_1"`, `"elev_2"`, `"q1"`, `"q2"`, `"kopi"`, `"resto"`).
- **dur**: The dwell time in seconds (a number, `0` for non-exit events, otherwise the time spent in the queue).
- **count**: The current state of people joining `q1` or `q2` .

## Event Triggers

**Person Exits Elevator_01 (Cam_01)**:

```json
{
  "evt_type": "elev_exit",
  "cam": "c1",
  "ts": "2025-03-23T10:15:30.123Z",
  "t_id": "550e8400-e29b-41d4-a716-446655440000",
  "q_name": null,
  "loc": "elev_1",
  "dur": 0,
  "count": 0
}
```

**Person Exits Elevator_02 (Cam_04)**:

```json
{
  "evt_type": "elev_exit",
  "cam": "c4",
  "ts": "2025-03-23T10:15:31.456Z",
  "t_id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
  "q_name": null,
  "loc": "elev_2",
  "dur": 0,
  "count": 0
}
```

**Person Joins Queue_01 (Cam_04 or Cam_05)**:

- duration is 0.

```json
{
  "evt_type": "q_entry",
  "cam": "cam5",
  "ts": "2025-03-23T10:15:32.789Z",
  "t_id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
  "q_name": "q1",
  "loc": "q1",
  "duration": 0,
  "count": 0
}
```

**Person Leaves Queue_01 (Cam_04 or Cam_05)**:

- If a person “A” join Queue 1 and abandon the Queue 1, this evt_type: q_exit for this person “A” will not be triggered .
- duration is the dwell time in seconds (e.g., 312 seconds).

```json
{
  "evt_type": "q_exit",
  "cam": "cam_04",
  "ts": "2025-03-23T10:20:45.123Z",
  "t_id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
  "q_name": "q1",
  "loc": "q1",
  "dur": 312,
  "count": 0
}
```

**Person Enters Kopitiam (Cam_03)**:

```json
{
  "evt_type": "kopi_entry",
  "cam": "cam_03",
  "ts": "2025-03-23T10:21:00.456Z",
  "t_id": "d1f7c8a0-9e4b-11d1-80b4-00c04fd430c9",
  "q_name": null,
  "loc": "kopi",
  "dur": 0,
  "count": 0
}
```

**Person Joins Queue_02 (Cam_02)**:

- duration is 0.

```json
{
  "evt_type": "q_entry",
  "cam": "cam_02",
  "ts": "2025-03-23T10:22:10.567Z",
  "t_id": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  "q_name": "q2",
  "loc": "q2",
  "dur": 0,
  "count": 0
}
```

**Person Leaves Queue_02 and Enters the Restaurant to be served (Cam_02)**:

- duration is the dwell time in seconds (e.g., 171 seconds).

```json
{
  "evt_type": "q_exit_resto",
  "cam": "c2",
  "ts": "2025-03-23T10:25:02.123Z",
  "t_id": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  "**q_name": "q2",**
  "loc": "resto",
  "dur": 171,
  "count": 0
}
```

**Person Leaves Queue_02 Without Entering the Restaurant to be served (Cam_02)**:

- duration is the dwell time in seconds (e.g., 171 seconds).

```json
{
  "evt_type": "q_exit",
  "cam": "cam2",
  "ts": "2025-03-23T10:25:00.890Z",
  "t_id": "a1b2c3d4-e5f6-47g8-h9i0-j1k2l3m4n5o6",
  "q_name": "q2",
  "loc": "q2",
  "dur": 171,
  "count": 0
}
```

**Person Entering Restaurant Without Queuing ( updated at 1st April )**

```json
{
  "evt_type": "resto_entry",
  "cam": "c2",
  "ts": "2025-03-31T14:20:45.678Z",
  "t_id": "abc123-xyz789",
  "q_name": null,
  "loc": "resto",
  "dur": 0,
  "count": 0
}
```

**Current State of Queue 1 ( updated at 1st April )**

- **Purpose**: Reports the number of people currently in Queue 1 (monitored by c4 or c5).

```json
{
  "evt_type": "count_state",
  "cam": "c4",
  "ts": 2025-03-31T14:30:00.000Z,
  "t_id": null,
  "q_name": "q1",
  "loc": "q1",
  "dur": 0,
  "count": 15  // Number of people currently in Queue 1
}
```

**Current State of Queue 2 ( updated at 1st April )**

- **Purpose**: Reports the number of people currently in Queue 2 (monitored by c2).

```json
{
  "evt_type": "count_state",
  "cam": "c2",
  "ts": 2025-03-31T14:30:00.000Z,
  "t_id": null,
  "q_name": "q2",
  "loc": "q2",
  "dur": 0,
  "count": 20 // Number of people currently in Queue 2
}
```

---

**Repeated Kopitiam Entries ( updated at 1st April )**

Event will be sent at the **end of the operating day** and should contain the **Repeated Kopitiam Entries** value.

```json
{
  "evt_type": "du_kopi_entry",
  "cam": null,
  "ts": "2025-03-31T23:59:59.000Z",
  "t_id": null,
  "q_name": null,
  "loc": "kopi", // Location is Kopi
  "dur": 0,
  "count": 30 // The Repeated Kopitiam Entries value           // The date for which this value applies
}
```

**Abandon Queue 1 ( no queue 2 ) ( updated at 1st April )**

Event will be sent when people joined the queue 1 and abandon the queue.

```json
{
  "evt_type": "q_abandon",
  "cam": null,
  "ts": "2025-03-31T23:59:59.000Z",
  "t_id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
  "q_name": "q1",
  "loc": "q1", // Location Q1
  "dur": 120, // duration
  "count": 0
}
```

### ✅ Event Triggers Interpretation

- **Person exits Elevator 01 (cam 01)**:
  - `evt_type`: `"elev_exit"`, `cam`: `"c1"`, `q_name`: `null`, `loc`: `"elev_1"`, `dur`: `0`, `t_id`: `"uuid"` , `count`: `0`
- **Person exits Elevator 02 (cam 04)**:
  - `evt_type`: `"elev_exit"`, `cam`: `"c4"`, `q_name`: `null`, `loc`: `"elev_2"`, `dur`: `0`, `t_id`: `"uuid"` , `count`: `0`
- **Person joins Queue 01 (cam 04 or 05)**:
  - `evt_type`: `"q_entry"`, `cam`: `"c4"` or `"c5"`, `q_name`: `"q1"`, `loc`: `"q1"`, `dur`: `0`, `t_id`: `"uuid"` , `count`: `0`
- **Person leaves Queue 01 (cam 04 or 05)**:
  - `evt_type`: `"q_exit"`, `cam`: `"c4"` or `"c5"`, `q_name`: `"q1"`, `loc`: `"q1"`, `dur`: time spent in queue, `t_id`: `"uuid"` , `count`: `0`
- **Person enters Kopitiam (cam 03)**:
  - `evt_type`: `"kopi_entry"`, `cam`: `"c3"`, `q_name`: `null`, `loc`: `"kopi"`, `dur`: `0`, `t_id`: `"uuid"` , `count`: `0`
- **Person joins Queue 02 (cam 02)**:
  - `evt_type`: `"q_entry"`, `cam`: `"c2"`, `q_name`: `"q2"`, `loc`: `"q2"`, `dur`: `0`, `t_id`: `"uuid"` , `count`: `0`
- **Person leaves Queue 02 and enters the restaurant to be served (cam 02)**:
  - `evt_type`: `"q_exit_resto"`, `cam`: `"c2"`, `q_name`: `"q2"`, `loc`: `"resto"`, `dur`: time spent in queue, `t_id`: `"uuid"` , `count`: `0`
- **Person leaves Queue 02 without entering the restaurant to be served (cam 02)**:
  - `evt_type`: `"q_exit"`, `cam`: `"c2"`, `q_name`: `"q2"`, `loc`: `"q2"`, `dur`: time spent in queue, `t_id`: `"uuid"` , `count`: `0`
- **Person entering the restaurant to be served without queuing (cam 02)**:
  - `evt_type`: `"resto_entry"`, `cam`: `"c2"`, `q_name`: `null`, `loc`: `resto`, `dur`: 0 `t_id`: `"uuid"` , `count`: `0`
- **Current State of queue 1 (cam 04 / cam 5)**:
  - `evt_type`: `"count_state"`, `cam`: `"c4"`, `q_name`: `q1`, `loc`: `q1`, `dur`: 0 `t_id`: `null` , `count:` value of current count.
- **Current State of queue 2 (cam 2)**:
  - `evt_type`: `"count_state"`, `cam`: `"c2"`, `q_name`: `q2`, `loc`: `q2`, `dur`: 0 `t_id`: `null` , `count:` value of current count.
- **Abandon Queue 1**
  - `evt_type`: `"q_abandon"`, `cam`: `"c2"`, `q_name`: `q1`, `loc`: `q1`, `dur`: value `t_id`: `null` , `count: 0`
