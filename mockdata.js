const amqp = require('amqplib')
const { v4: uuidv4 } = require('uuid')

const QUEUE_A = 'QUEUE_A'
const RABBITMQ_URL =
  'amqps://fqkkekng:<EMAIL>/fqkkekng'

async function publishMockEvents(
  limitMilliseconds = 300000,
  delayInterval = 1000
) {
  let connection
  let channel
  const queueState = { q1: 0, q2: 0 } // Centralized queue state
  let kopiRepeatCount = 0 // Track repeated Kopitiam entries
  const stateLock = { isLocked: false, queue: [] } // Simple lock for queueState updates

  try {
    connection = await amqp.connect(RABBITMQ_URL)
    channel = await connection.createChannel()
    await channel.assertQueue(QUEUE_A, { durable: true })
    console.log(`Connected to RabbitMQ and asserted durable queue: ${QUEUE_A}`)

    const getTimestampWithOffset = (baseTime, offsetSeconds) => {
      const time = new Date(baseTime)
      time.setSeconds(time.getSeconds() + offsetSeconds)
      return time.toISOString()
    }

    const sendToQueuePromise = (queue, message) => {
      const messageStr = [
        message.evt_type,
        message.cam || '',
        message.ts,
        message.t_id || '',
        message.q_name || '',
        message.loc,
        message.dur,
        message.count,
        'clove',
      ].join(',')

      return new Promise((resolve, reject) => {
        const onDrain = () => {
          channel.off('drain', onDrain)
          channel.off('error', onError)
          resolve()
        }

        const onError = (err) => {
          channel.off('drain', onDrain)
          channel.off('error', onError)
          reject(err)
        }

        channel.once('drain', onDrain)
        channel.once('error', onError)

        const sent = channel.sendToQueue(
          queue,
          Buffer.from(`"${messageStr}"`),
          {
            persistent: true,
            contentType: 'text/plain',
          }
        )

        if (sent) {
          channel.off('drain', onDrain)
          channel.off('error', onError)
          resolve()
        }
      })
    }

    // Lock mechanism to update queueState safely
    const updateQueueState = (updateFn) => {
      return new Promise((resolve) => {
        const executeUpdate = () => {
          updateFn()
          stateLock.isLocked = false
          if (stateLock.queue.length > 0) {
            const next = stateLock.queue.shift()
            next()
          }
          resolve()
        }

        if (stateLock.isLocked) {
          stateLock.queue.push(executeUpdate)
        } else {
          stateLock.isLocked = true
          executeUpdate()
        }
      })
    }

    const simulateQueueJourney = async (
      cam,
      q_name,
      loc,
      baseTime,
      allowServed = false
    ) => {
      const t_id = uuidv4()
      const entryTime = getTimestampWithOffset(baseTime, 0)

      // q_entry event
      const entryEvent = {
        evt_type: 'q_entry',
        cam,
        ts: entryTime,
        t_id,
        q_name,
        loc,
        dur: 0,
        count: 0,
      }
      await updateQueueState(() => {
        if (q_name === 'q1') queueState.q1++
        if (q_name === 'q2') queueState.q2++
      })
      await sendToQueuePromise(QUEUE_A, entryEvent)
      console.log(`Sent message: ${Object.values(entryEvent).join(',')}`)

      // Randomly decide to abandon (only for q1)
      if (q_name === 'q1' && Math.random() > 0.7) {
        const abandonDuration = Math.floor(Math.random() * (300 - 10 + 1)) + 10
        const abandonTime = getTimestampWithOffset(baseTime, abandonDuration)
        const abandonEvent = {
          evt_type: 'q_abandon',
          cam: null,
          ts: abandonTime,
          t_id,
          q_name,
          loc,
          dur: abandonDuration,
          count: 0,
        }
        await updateQueueState(() => {
          queueState.q1--
        })
        await sendToQueuePromise(QUEUE_A, abandonEvent)
        console.log(`Sent message: ${Object.values(abandonEvent).join(',')}`)
        return
      }

      // Simulate queue duration (10 to 600 seconds)
      let queueDuration
      const durationGroup = Math.random()

      if (durationGroup < 0.25) {
        // Group 1: Under 1 minute (10-59 seconds)
        queueDuration = Math.floor(Math.random() * (59 - 10 + 1)) + 10
      } else if (durationGroup < 0.6) {
        // Group 2: 1-5 minutes (60-299 seconds)
        queueDuration = Math.floor(Math.random() * (299 - 60 + 1)) + 60
      } else if (durationGroup < 0.85) {
        // Group 3: 5-10 minutes (300-599 seconds)
        queueDuration = Math.floor(Math.random() * (599 - 300 + 1)) + 300
      } else {
        // Group 4: Over 10 minutes (600+ seconds)
        queueDuration = Math.floor(Math.random() * (900 - 600 + 1)) + 600
      }

      const exitTime = getTimestampWithOffset(baseTime, queueDuration)

      await new Promise((resolve) => setTimeout(resolve, queueDuration * 100))

      // Only allow q_exit_resto for c2 ව

      // Only allow q_exit_resto for c2, q2
      const isServed = allowServed && Math.random() > 0.5
      const exitEvent = {
        evt_type: isServed ? 'q_exit_resto' : 'q_exit',
        cam,
        ts: exitTime,
        t_id,
        q_name,
        loc: isServed ? 'resto' : loc,
        dur: queueDuration,
        count: 0,
      }
      await updateQueueState(() => {
        if (q_name === 'q1') queueState.q1--
        if (q_name === 'q2') queueState.q2--
      })
      await sendToQueuePromise(QUEUE_A, exitEvent)
      console.log(`Sent message: ${Object.values(exitEvent).join(',')}`)
    }

    // Main simulation loop
    const startTime = Date.now()
    const runningPromises = []

    while (Date.now() - startTime < limitMilliseconds) {
      const currentBaseTime = new Date()

      // Standalone events
      const standaloneEvents = [
        {
          evt_type: 'elev_exit',
          cam: 'c1',
          q_name: null,
          loc: 'elev_1',
          dur: 0,
        },
        {
          evt_type: 'elev_exit',
          cam: 'c4',
          q_name: null,
          loc: 'elev_2',
          dur: 0,
        },
        {
          evt_type: 'kopi_entry',
          cam: 'c3',
          q_name: null,
          loc: 'kopi',
          dur: 0,
        },
        {
          evt_type: 'resto_entry',
          cam: 'c2',
          q_name: null,
          loc: 'resto',
          dur: 0,
        },
      ]

      if (Math.random() > 0.0) {
        const event =
          standaloneEvents[Math.floor(Math.random() * standaloneEvents.length)]
        const message = {
          evt_type: event.evt_type,
          cam: event.cam,
          ts: getTimestampWithOffset(currentBaseTime, 0),
          t_id: uuidv4(),
          q_name: event.q_name,
          loc: event.loc,
          dur: event.dur,
          count: event.evt_type === 'kopi_entry' ? (kopiRepeatCount++, 0) : 0,
        }
        runningPromises.push(sendToQueuePromise(QUEUE_A, message))
        console.log(`Sent message: ${Object.values(message).join(',')}`)
      }

      // Simulate queue journeys (run in parallel)
      if (Math.random() > 0.2) {
        runningPromises.push(
          simulateQueueJourney('c4', 'q1', 'q1', currentBaseTime, false)
        )
      }
      if (Math.random() > 0.2) {
        runningPromises.push(
          simulateQueueJourney('c2', 'q2', 'q2', currentBaseTime, true)
        )
      }

      // Send count_state events occasionally
      if (Math.random() > 0.0) {
        const q1State = {
          evt_type: 'count_state',
          cam: 'c4',
          ts: getTimestampWithOffset(currentBaseTime, 0),
          t_id: null,
          q_name: 'q1',
          loc: 'q1',
          dur: 0,
          count: queueState.q1,
        }
        runningPromises.push(sendToQueuePromise(QUEUE_A, q1State))
        console.log(`Sent message: ${Object.values(q1State).join(',')}`)
      }
      if (Math.random() > 0.0) {
        const q2State = {
          evt_type: 'count_state',
          cam: 'c2',
          ts: getTimestampWithOffset(currentBaseTime, 0),
          t_id: null,
          q_name: 'q2',
          loc: 'q2',
          dur: 0,
          count: queueState.q2,
        }
        runningPromises.push(sendToQueuePromise(QUEUE_A, q2State))
        console.log(`Sent message: ${Object.values(q2State).join(',')}`)
      }

      await new Promise((resolve) => setTimeout(resolve, delayInterval))
    }

    // Wait for all pending promises to finish
    console.log(
      `Simulation limit (${limitMilliseconds}ms) elapsed. Waiting for all promises to finish...`
    )
    const intervalIdQ1 = setInterval(async () => {
      const q1State = {
        evt_type: 'count_state',
        cam: 'c4',
        ts: getTimestampWithOffset(new Date(), 0),
        t_id: null,
        q_name: 'q1',
        loc: 'q1',
        dur: 0,
        count: queueState.q1,
      }
      await sendToQueuePromise(QUEUE_A, q1State)
      console.log(`Sent periodic message: ${Object.values(q1State).join(',')}`)
    }, 1000)
    const intervalIdQ2 = setInterval(async () => {
      // Send final q2State
      const q2State = {
        evt_type: 'count_state',
        cam: 'c2',
        ts: getTimestampWithOffset(new Date(), 0),
        t_id: null,
        q_name: 'q2',
        loc: 'q2',
        dur: 0,
        count: queueState.q2,
      }
      await sendToQueuePromise(QUEUE_A, q2State)
      console.log(`Sent message: ${Object.values(q2State).join(',')}`)
    }, 1000)

    await Promise.all(runningPromises)
    clearInterval(intervalIdQ1)
    clearInterval(intervalIdQ2)

    // Send du_kopi_entry at the end
    const endTime = new Date()
    endTime.setHours(23, 59, 59, 0)
    const kopiRepeatEvent = {
      evt_type: 'du_kopi_entry',
      cam: null,
      ts: endTime.toISOString(),
      t_id: null,
      q_name: null,
      loc: 'kopi',
      dur: 0,
      count: kopiRepeatCount,
    }
    await sendToQueuePromise(QUEUE_A, kopiRepeatEvent)
    console.log(`Sent message: ${Object.values(kopiRepeatEvent).join(',')}`)

    // Send final q1State
    const q1State = {
      evt_type: 'count_state',
      cam: 'c4',
      ts: getTimestampWithOffset(new Date(), 0),
      t_id: null,
      q_name: 'q1',
      loc: 'q1',
      dur: 0,
      count: queueState.q1,
    }
    await sendToQueuePromise(QUEUE_A, q1State)
    console.log(`Sent message: ${Object.values(q1State).join(',')}`)

    // Send final q2State
    const q2State = {
      evt_type: 'count_state',
      cam: 'c2',
      ts: getTimestampWithOffset(new Date(), 0),
      t_id: null,
      q_name: 'q2',
      loc: 'q2',
      dur: 0,
      count: queueState.q2,
    }
    await sendToQueuePromise(QUEUE_A, q2State)
    console.log(`Sent message: ${Object.values(q2State).join(',')}`)

    console.log('All events processed. Stopping the simulation...')
  } catch (error) {
    console.error('Error:', error)
  } finally {
    if (channel) await channel.close()
    if (connection) await connection.close()
    console.log('Connection closed')
  }
}

publishMockEvents(1000 * 60 * 1, 500) // Run simulation for 1 minutes with 1000ms delay
