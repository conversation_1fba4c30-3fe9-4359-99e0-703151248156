import { config } from '@/config'
import { initializeApp } from 'firebase/app'
import { getAuth, signInWithCustomToken } from 'firebase/auth'
import { getFirestore } from 'firebase/firestore'
import admin from 'firebase-admin'
import fs from 'fs'
import logger from '@/lib/logger'

let authPromise: Promise<any> | null = null

// Initialize Firebase Admin SDK
const serviceAccountPath = config.firebase.serviceAccountPath

// Check if the service account file exists
try {
  const serviceAccount = JSON.parse(fs.readFileSync(serviceAccountPath, 'utf8'))

  const params = {
    type: serviceAccount.type,
    projectId: serviceAccount.project_id,
    privateKeyId: serviceAccount.private_key_id,
    privateKey: serviceAccount.private_key,
    clientEmail: serviceAccount.client_email,
    clientId: serviceAccount.client_id,
    authUri: serviceAccount.auth_uri,
    tokenUri: serviceAccount.token_uri,
    authProviderX509CertUrl: serviceAccount.auth_provider_x509_cert_url,
    clientC509CertUrl: serviceAccount.client_x509_cert_url,
    universe_domain: serviceAccount.universe_domain,
  }

  admin.initializeApp({
    credential: admin.credential.cert(params),
  })
} catch (error) {
  logger.error(
    `Failed to initialize Firebase Admin SDK: ${error instanceof Error ? error.message : String(error)}`
  )
  throw new Error(
    'Firebase service account configuration is invalid or missing'
  )
}

// Configure Firebase Client SDK
const firebaseConfig = {
  apiKey: config.firebase.apiKey,
  authDomain: config.firebase.authDomain,
  projectId: config.firebase.projectId,
  storageBucket: config.firebase.storageBucket,
  messagingSenderId: config.firebase.messagingSenderId,
  appId: config.firebase.appId,
  measurementId: config.firebase.measurementId,
}

// Initialize Firebase Client SDK
const app = initializeApp(firebaseConfig)
const dbFirebase = getFirestore(app)
const auth = getAuth(app)

// Function to authenticate with custom token
async function authenticateWithCustomToken(uid: string) {
  try {
    // Return existing auth promise if it exists
    if (authPromise) {
      return authPromise
    }

    // Check if user is already authenticated with the same UID
    if (auth.currentUser && auth.currentUser.uid === uid) {
      logger.info(`User already authenticated: ${auth.currentUser.uid}`)
      return Promise.resolve(auth.currentUser)
    }

    // Create and store the authentication promise
    authPromise = (async () => {
      const customToken = await admin.auth().createCustomToken(uid)
      const userCredential = await signInWithCustomToken(auth, customToken)
      logger.info(`User authenticated: ${userCredential.user.uid}`)
      return userCredential.user
    })()

    return authPromise
  } catch (error) {
    // Reset promise on error
    authPromise = null
    logger.error('Error authenticating with custom token')
    throw error
  }
}

// Perform authentication on initialization
const uid = config.firebase.uidAccount

auth.onAuthStateChanged((user) => {
  if (user) {
    logger.info(`User is authenticated on state change: ${user.uid}`)
  } else {
    logger.info('User is not authenticated, authenticating...')
    authenticateWithCustomToken(uid).catch((error) => {
      logger.error(`Authentication failed: ${error}`)
    })
  }
})

authenticateWithCustomToken(uid).catch((error) => {
  logger.error('Failed to authenticate:', error)
})

// Export dbFirebase for use in other modules
export { dbFirebase, authenticateWithCustomToken }
