import { Client, LocalAuth } from 'whatsapp-web.js'
import qrcode from 'qrcode-terminal'
import logger from '@/lib/logger'
import { config } from '@/config'

class WhatsAppService {
  private client: Client | null = null
  private isReady = false
  private notificationRecipients: string[] =
    config.whatsapp?.notificationRecipients || []
  private shutdownPromise: Promise<void> | null = null
  private isShuttingDown = false

  constructor() {
    this.client = new Client({
      authStrategy: new LocalAuth(),
      puppeteer: {
        args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-gpu'],
        handleSIGINT: false,
        handleSIGTERM: false,
      },
    })

    this.setupEventHandlers()
  }

  private setupEventHandlers() {
    if (!this.client) return

    this.client.on('qr', (qr) => {
      logger.info('WhatsApp QR code generated. Scan with your phone:')
      qrcode.generate(qr, { small: true })
    })

    this.client.on('ready', () => {
      this.isReady = true
      logger.info('WhatsApp client is ready')
    })

    this.client.on('auth_failure', (msg) => {
      logger.error('WhatsApp authentication failed', { message: msg })
    })

    this.client.on('disconnected', (reason) => {
      this.isReady = false
      logger.warn('WhatsApp client disconnected', { reason })
    })
  }

  public async initialize(): Promise<boolean> {
    try {
      if (!this.client) {
        logger.error('WhatsApp client not instantiated')
        return false
      }

      await this.client.initialize()
      return true
    } catch (error) {
      logger.error('Failed to initialize WhatsApp client', {
        error: error instanceof Error ? error.message : String(error),
      })
      return false
    }
  }

  public async shutdown(): Promise<void> {
    if (this.shutdownPromise) {
      return this.shutdownPromise
    }

    if (this.isShuttingDown) {
      logger.info('Shutdown already in progress, skipping redundant call.')
      return
    }

    this.isShuttingDown = true
    this.shutdownPromise = (async () => {
      try {
        if (this.client) {
          logger.info('Shutting down WhatsApp client...')
          await this.client.destroy()
          if (this.client.pupBrowser) {
            await this.client.pupBrowser.close()
            logger.info('Puppeteer browser closed.')
          }
          this.client = null
          this.isReady = false
          logger.info('WhatsApp client shutdown complete')
        } else {
          logger.info('No WhatsApp client to shutdown')
        }
      } catch (error) {
        logger.error('Error shutting down WhatsApp client', {
          error: error instanceof Error ? error.message : String(error),
        })
      } finally {
        this.isShuttingDown = false
        this.shutdownPromise = null
      }
    })()

    await this.shutdownPromise
  }

  public async sendQueueAlert(message: string): Promise<boolean> {
    if (!this.isReady || !this.client) {
      logger.warn('WhatsApp client not ready, cannot send alert')
      return false
    }

    try {
      const results = await Promise.all(
        this.notificationRecipients.map(async (number) => {
          try {
            await this.client!.sendMessage(`${number}@c.us`, message)
            return true
          } catch (error) {
            logger.error(`Failed to send WhatsApp alert to ${number}`, {
              error: error instanceof Error ? error.message : String(error),
            })
            return false
          }
        })
      )

      return results.some((result) => result)
    } catch (error) {
      logger.error('Error sending WhatsApp alert', {
        error: error instanceof Error ? error.message : String(error),
      })
      return false
    }
  }
}

// Singleton instance
let whatsAppInstance: WhatsAppService | null = null

export const getWhatsAppService = (): WhatsAppService => {
  if (!whatsAppInstance) {
    whatsAppInstance = new WhatsAppService()
  }
  return whatsAppInstance
}
