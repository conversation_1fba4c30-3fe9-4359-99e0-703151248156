import { config, isProduction } from '@/config'
import { drizzle } from 'drizzle-orm/node-postgres'
import { Pool } from 'pg'
import fs from 'fs'
import path from 'path'

// Resolve SSL cert content if applicable
let sslCert: string | undefined = undefined

if (isProduction() && config.postgresql.ssl && config.postgresql.sslCertPath) {
  const certPath = path.resolve(process.cwd(), config.postgresql.sslCertPath)
  console.log('Database Cert Path:', certPath)

  if (!fs.existsSync(certPath)) {
    console.error(`❌ SSL cert not found at path: ${certPath}`)
    process.exit(1)
  }

  sslCert = fs.readFileSync(certPath, 'utf-8')
}

// Configure SSL options based on environment
const sslConfig =
  isProduction() && config.postgresql.ssl
    ? {
        ssl: {
          rejectUnauthorized: true,
          ca: sslCert,
        },
      }
    : {}

const pool = new Pool({
  host: config.postgresql.host,
  port: config.postgresql.port,
  user: config.postgresql.user,
  password: config.postgresql.password,
  database: config.postgresql.database,
  ...sslConfig,
})

export const db = drizzle(pool)
