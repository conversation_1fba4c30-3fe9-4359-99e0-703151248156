import { env } from './env'

export const fireBaseCollection = {
  SECTIONS_METRICS: 'sectionsMetrics',
  STORE_METRICS: 'storeMetrics',
  QUEUE_METRICS: 'queueMetrics',
  QUEUE_SUMMARIES: 'queueSummaries',
} as const

export const config = {
  env: env.NODE_ENV,
  tenantId: env.TENANT_ID,

  server: {
    port: env.PORT,
    corsOrigin: env.CORS_ORIGIN,
  },

  logging: {
    level: env.LOG_LEVEL,
  },

  rabbitmq: {
    protocol: env.RABBITMQ_PROTOCOL,
    hostname: env.RABBITMQ_HOSTNAME,
    port: env.RABBITMQ_PORT,
    username: env.RABBITMQ_USERNAME,
    password: env.RABBITMQ_PASSWORD,
    vhost: env.RABBITMQ_VHOST,
    heartbeat: env.RABBITMQ_HEARTBEAT,
    queueName: env.RABBITMQ_QUEUE_NAME,
    initialRetryDelayMs: env.RABBITMQ_INITIAL_RETRY_DELAY_MS,
    maxRetryDelayMs: env.RABBITMQ_MAX_RETRY_DELAY_MS,
  },

  postgresql: {
    host: env.POSTGRES_HOST,
    port: env.POSTGRES_PORT,
    user: env.POSTGRES_USER,
    password: env.POSTGRES_PASSWORD,
    database: env.POSTGRES_DATABASE,
    ssl: env.POSTGRES_SSL_ENABLED,
    sslCertPath: env.POSTGRES_SSL_CERT_PATH,
  },

  firebaseCronUpdate: {
    storeMetrics: env.STORE_METRICS_UPDATE_INTERVAL,
    queueMetrics: env.QUEUE_METRICS_UPDATE_INTERVAL,
    queueSummary: env.QUEUE_SUMMARY_UPDATE_INTERVAL,
    sectionsMetrics: env.SECTIONS_METRICS_UPDATE_INTERVAL,
  },

  firebase: {
    apiKey: env.FIREBASE_API_KEY,
    authDomain: env.FIREBASE_AUTH_DOMAIN,
    projectId: env.FIREBASE_PROJECT_ID,
    storageBucket: env.FIREBASE_STORAGE_BUCKET,
    messagingSenderId: env.FIREBASE_MESSAGING_SENDER_ID,
    appId: env.FIREBASE_APP_ID,
    measurementId: env.FIREBASE_MEASUREMENT_ID,
    serviceAccountPath: env.FIREBASE_SERVICE_ACCOUNT_PATH,
    uidAccount: env.FIREBASE_UID_ACCOUNT,
  },

  time: {
    timezone: env.TIMEZONE,
    timeSlotMinutes: env.TIME_SLOT_MINUTES,
    rollingWindowMinutes: env.ROLLING_WINDOW_MINUTES,
    operationalHours: {
      bufferMinutes: env.OPERATING_HOUR_BUFFER_MINUTES,
      checkIntervalMs: env.OPERATIONAL_HOURS_CHECK_INTERVAL_MS,
    },
  },

  debounce: {
    delayMs: env.DEBOUNCE_DELAY_MS,
    maxWaitMs: env.DEBOUNCE_MAX_WAIT_MS,
  },

  skipValidation: env.SKIP_VALIDATION,

  whatsapp: {
    enabled: env.WHATSAPP_ENABLED,
    notificationRecipients: env.WHATSAPP_NOTIFICATION_RECIPIENTS,
  },

  alert: {
    thresholdMultiplier: env.QUEUE_THRESHOLD_MULTIPLIER,
    expiryMonths: env.QUEUE_ALERT_EXPIRY_MONTHS,
  },

  reset: {
    dailyResetTime: env.DAILY_RESET_TIME,
  },
} as const

// Type for the entire config
export type Config = typeof config

// Export environment type checker
export function isProduction(): boolean {
  return config.env === 'production'
}

export function isDevelopment(): boolean {
  return config.env === 'development'
}

export function isTest(): boolean {
  return config.env === 'test'
}
