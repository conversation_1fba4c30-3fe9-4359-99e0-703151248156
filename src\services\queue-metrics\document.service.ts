import { dbFirebase } from '@/firebase/dbFirebase'
import logger from '@/lib/logger'
import {
  doc,
  DocumentReference,
  Timestamp,
  updateDoc,
} from 'firebase/firestore'
import { TUpdateResult } from './types'
import { config, fireBaseCollection } from '@/config'

const docRef: DocumentReference = doc(
  dbFirebase,
  fireBaseCollection.QUEUE_METRICS,
  config.tenantId
)

/**
 * Updates only the inQueue values for entryQueue and seatingQueue in Firebase
 * This is used by the real-time handler to update queue counts without affecting dwell times
 * @param q1Count The current count for Q1 (entryQueue)
 * @param q2Count The current count for Q2 (seatingQueue)
 * @returns Result of the update operation
 */
export const updateQueueCounts = async (
  q1Count: number,
  q2Count: number
): Promise<TUpdateResult> => {
  try {
    // Create update data with only the fields we want to update
    const updateData = {
      'entryQueue.inQueue': q1Count,
      'seatingQueue.inQueue': q2Count,
      tenantId: config.tenantId,
      updatedAt: Timestamp.now(),
    }

    updateMetricsDocument(updateData)

    logger.info('Updated queue counts in Firebase', {
      q1Count,
      q2Count,
      updatedAt: new Date(),
    })

    return {
      success: true,
      error: null,
    }
  } catch (error) {
    logger.error('Error updating queue counts:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    }
  }
}

/**
 * Updates only the avgDwellTime values for entryQueue and seatingQueue in Firebase
 * This is used to update dwell times without affecting inQueue counts
 * @param q1DwellTime The average dwell time for Q1 (entryQueue) in seconds
 * @param q2DwellTime The average dwell time for Q2 (seatingQueue) in seconds
 * @returns Result of the update operation
 */
export const updateDwellTimes = async (
  q1DwellTime: number,
  q2DwellTime: number
): Promise<TUpdateResult> => {
  try {
    // Create update data with only the dwell time fields
    const updateData = {
      'entryQueue.avgDwellTime': q1DwellTime,
      'seatingQueue.avgDwellTime': q2DwellTime,
      tenantId: config.tenantId,
      updatedAt: Timestamp.now(),
    }

    await updateMetricsDocument(updateData)

    logger.info('Updated dwell times in Firebase', {
      q1DwellTime,
      q2DwellTime,
      updatedAt: Timestamp.now(),
    })

    return {
      success: true,
      error: null,
    }
  } catch (error) {
    logger.error('Error updating dwell times:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    }
  }
}

/**
 * Shared function to perform Firestore updates.
 * @param updates Record of fields to update in the document.
 * @returns Promise<void>
 */
const updateMetricsDocument = async (
  updates: Record<string, any>
): Promise<void> => {
  try {
    await updateDoc(docRef, updates)
  } catch (err) {
    logger.error('Error updating Firestore document:', { error: err })
    throw err
  }
}

// Export shared utilities
export { docRef }
