// In-memory state for queue metrics
export type TQueueMetricsState = {
  q1Count: number
  q2Count: number
  latestQ1Ts: Date | null
  latestQ2Ts: Date | null
}

// Pending updates for debouncing
export type TPendingQueueUpdates = {
  q1Count: number
  q2Count: number
}

// Result type for dwell time queries
export type TDwellTimeResult = {
  q1AverageDwellTime: number
  q2AverageDwellTime: number
}

// Result type for document updates
export type TUpdateResult = {
  success: boolean
  error: string | null
}
