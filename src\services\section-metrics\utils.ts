import { PEAK_SECTION_VALUES, TSection, TPeakSectionValue } from './types'

// Type guards and helpers
export const isPeakSection = (
  section: TSection
): section is TPeakSectionValue =>
  PEAK_SECTION_VALUES.includes(section as TPeakSectionValue)

// // Helper to ensure consistent conversion of peakTime to string
// export const formatPeakTime = (time: Date | string): string =>
//   time instanceof Date ? time.toISOString() : time
