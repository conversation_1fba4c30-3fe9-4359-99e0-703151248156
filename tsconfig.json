{"compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "noEmit": true, "baseUrl": "./", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*", "scripts/**/*", "*.ts"], "exclude": ["dist", "node_modules", "tools", "**/*.test.ts"]}