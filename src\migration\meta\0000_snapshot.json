{"id": "98538a34-8970-4464-aa83-12b2af8a4bee", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.events": {"name": "events", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": false, "default": "gen_random_uuid()"}, "evt_type": {"name": "evt_type", "type": "text", "primaryKey": false, "notNull": true}, "cam": {"name": "cam", "type": "text", "primaryKey": false, "notNull": true}, "ts": {"name": "ts", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "t_id": {"name": "t_id", "type": "text", "primaryKey": false, "notNull": false}, "q_name": {"name": "q_name", "type": "text", "primaryKey": false, "notNull": false}, "loc": {"name": "loc", "type": "text", "primaryKey": false, "notNull": true}, "dur": {"name": "dur", "type": "integer", "primaryKey": false, "notNull": true}, "count": {"name": "count", "type": "integer", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.temp_queues": {"name": "temp_queues", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": false, "notNull": false, "default": "gen_random_uuid()"}, "evt_type": {"name": "evt_type", "type": "text", "primaryKey": false, "notNull": true}, "cam": {"name": "cam", "type": "text", "primaryKey": false, "notNull": true}, "ts": {"name": "ts", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "t_id": {"name": "t_id", "type": "text", "primaryKey": false, "notNull": false}, "q_name": {"name": "q_name", "type": "text", "primaryKey": false, "notNull": false}, "loc": {"name": "loc", "type": "text", "primaryKey": false, "notNull": true}, "dur": {"name": "dur", "type": "integer", "primaryKey": false, "notNull": true}, "count": {"name": "count", "type": "integer", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"ts_idx": {"name": "ts_idx", "columns": [{"expression": "ts", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}