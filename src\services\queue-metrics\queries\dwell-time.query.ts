import { db } from '@/database/db'
import { tempQueues } from '@/database/schema'
import { sql } from 'drizzle-orm'
import { TDwellTimeResult } from '../types'
import logger from '@/lib/logger'
import { updateDwellTimes } from '../document.service'
import { config } from '@/config'
import { getRollingWindowStart } from '@/utils/dateFormat.utils'

// Define the type for the query result
type DwellTimeQueryResult = {
  q1_average_dwell_time: number | null
  q2_average_dwell_time: number | null
}

// --- Caching for dwell times ---
let cachedDwellTimes: TDwellTimeResult = {
  q1AverageDwellTime: 0,
  q2AverageDwellTime: 0,
}

export async function refreshAverageDwellTimes() {
  cachedDwellTimes = await getAverageDwellTimes()
}

export function getCachedAverageDwellTimes() {
  return cachedDwellTimes
}

/**
 * Gets the average dwell time for both Q1 and Q2 in a single query
 * @returns Object containing average dwell times for Q1 and Q2
 */
export const getAverageDwellTimes = async (): Promise<TDwellTimeResult> => {
  try {
    const windowStartTime = getRollingWindowStart(
      config.time.rollingWindowMinutes
    )

    // Combined query for both Q1 and Q2 average dwell times
    const result = await db.execute<DwellTimeQueryResult>(sql`
      WITH q1_dwell AS (
        SELECT ROUND(AVG(${tempQueues.dur}))::INT as avg_dwell_time
        FROM ${tempQueues}
        WHERE ${tempQueues.q_name} = 'q1'
          AND ${tempQueues.ts} >= ${windowStartTime}
          AND ${tempQueues.evt_type} IN ('q_exit')
      ),
      q2_dwell AS (
        SELECT ROUND(AVG(${tempQueues.dur}))::INT as avg_dwell_time
        FROM ${tempQueues}
        WHERE ${tempQueues.q_name} = 'q2'
          AND ${tempQueues.ts} >= ${windowStartTime}
          AND ${tempQueues.evt_type} IN ('q_exit', 'q_exit_resto')
      )
      SELECT
        (SELECT avg_dwell_time FROM q1_dwell) as q1_average_dwell_time,
        (SELECT avg_dwell_time FROM q2_dwell) as q2_average_dwell_time
    `)

    const q1AverageDwellTime = result.rows[0]?.q1_average_dwell_time ?? 0
    const q2AverageDwellTime = result.rows[0]?.q2_average_dwell_time ?? 0

    return {
      q1AverageDwellTime: Number(q1AverageDwellTime),
      q2AverageDwellTime: Number(q2AverageDwellTime),
    }
  } catch (error) {
    logger.error('Error getting average dwell times:', error)
    return {
      q1AverageDwellTime: 0,
      q2AverageDwellTime: 0,
    }
  }
}

/**
 * Updates the Firestore document with the average dwell times
 * Only updates if the values have changed from the previous update
 */
export async function updateAverageDwellTime(): Promise<void> {
  try {
    const dwellTimes = await getAverageDwellTimes()

    // Check if the values have changed
    if (
      cachedDwellTimes.q1AverageDwellTime === dwellTimes.q1AverageDwellTime &&
      cachedDwellTimes.q2AverageDwellTime === dwellTimes.q2AverageDwellTime
    ) {
      logger.debug(`No change in average dwell times, skipping update.`)
      return
    }

    updateDwellTimes(
      dwellTimes.q1AverageDwellTime,
      dwellTimes.q2AverageDwellTime
    )

    // Update the cache
    cachedDwellTimes = dwellTimes

    logger.info(`Updated average dwell times in Firestore`, {
      q1AverageDwellTime: dwellTimes.q1AverageDwellTime,
      q2AverageDwellTime: dwellTimes.q2AverageDwellTime,
    })
  } catch (error) {
    logger.error('Error updating average dwell times:', { error })
  }
}
