import { EventData } from '@/types/eventData.types'
import logger from '@/lib/logger'
import { config } from '@/config'
import { updateQueueCounts } from '../document.service'
import { debounce } from 'lodash'
import { TPendingQueueUpdates, TQueueMetricsState } from '../types'
import { checkQueueThresholds } from '@/services/queue-notification.service'

// In-memory state for queue metrics
const queueMetrics: TQueueMetricsState = {
  q1Count: 0,
  q2Count: 0,
  latestQ1Ts: null,
  latestQ2Ts: null,
}

// Pending updates for debouncing
let pendingQueueUpdates: TPendingQueueUpdates = {
  q1Count: 0,
  q2Count: 0,
}

// Debounce settings
const DEBOUNCE_WAIT_MS = config.debounce.delayMs
const DEBOUNCE_MAX_WAIT_MS = config.debounce.maxWaitMs

/**
 * Function to update queue counts to Firebase
 * This function is called when queue counts change
 * It only updates the inQueue values, not the avgDwellTime
 * The avgDwellTime is updated by a scheduled job in main.ts
 */
const updateQueueCountsToFirestore = async () => {
  try {
    // Check queue thresholds for notifications
    checkQueueThresholds(queueMetrics.q1Count)

    // Update Firebase with only the queue counts
    // This function preserves the existing avgDwellTime values
    const result = await updateQueueCounts(
      queueMetrics.q1Count,
      queueMetrics.q2Count
    )

    if (result.success) {
      // Reset pending updates
      pendingQueueUpdates = {
        q1Count: 0,
        q2Count: 0,
      }
    } else {
      logger.error('Failed to update queue counts:', result.error)
    }
  } catch (err) {
    logger.error('Error in updateQueueCountsToFirestore:', { error: err })
  }
}

// Debounced update function for queue metrics
const debouncedUpdateQueueMetrics = debounce(
  updateQueueCountsToFirestore,
  DEBOUNCE_WAIT_MS,
  { maxWait: DEBOUNCE_MAX_WAIT_MS }
)

/**
 * Main handler function for queue metrics
 * Processes events from RabbitMQ and updates queue metrics
 * @param eventData The event data from RabbitMQ
 */
export async function handleQueueEvent(eventData: EventData) {
  try {
    // Handle count_state events for queue counts
    // Note: 'count_state' is not in the EventType enum but is used in the RabbitMQ messages
    if (eventData.evt_type === ('count_state' as any)) {
      if (eventData.q_name === 'q1') {
        // Update q1 count
        queueMetrics.q1Count = eventData.count
        queueMetrics.latestQ1Ts = eventData.ts
        pendingQueueUpdates.q1Count = eventData.count

        // Trigger debounced update
        debouncedUpdateQueueMetrics()
        logger.debug('Q1 count updated:', {
          count: eventData.count,
          timestamp: eventData.ts,
        })
      } else if (eventData.q_name === 'q2') {
        // Update q2 count
        queueMetrics.q2Count = eventData.count
        queueMetrics.latestQ2Ts = eventData.ts
        pendingQueueUpdates.q2Count = eventData.count

        // Trigger debounced update
        debouncedUpdateQueueMetrics()
        logger.debug('Q2 count updated:', {
          count: eventData.count,
          timestamp: eventData.ts,
        })
      }
    }
  } catch (err) {
    logger.error('Error handling queue event:', { error: err })
  }
}

/**
 * Gets the current queue metrics state
 * @returns The current queue metrics state
 */
export function getQueueMetricsState(): TQueueMetricsState {
  return { ...queueMetrics }
}
