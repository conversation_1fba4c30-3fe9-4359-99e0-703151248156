import { format, parse, isBefore, isAfter, parseISO } from 'date-fns'
import { TZDate, tz } from '@date-fns/tz'
import { config } from '@/config'

/**
 * Returns the start time for a rolling window in the configured timezone.
 * @param windowMinutes Number of minutes for the rolling window
 * @returns TZDate object representing the window start time
 */
export function getRollingWindowStart(windowMinutes: number): TZDate {
  const now = toTZDate(new Date())
  return toTZDate(new Date(now.getTime() - windowMinutes * 60 * 1000))
}

/**
 * Formats a date to ISO string
 * @param date The date to format
 * @returns ISO string representation of the date
 */
export const toISOString = (date: Date | string): string => {
  const dateObj = date instanceof Date ? date : new Date(date)
  return dateObj.toISOString()
}

/**
 * Parses an ISO string to a Date object
 * @param isoString The ISO string to parse
 * @returns Date object
 */
export const fromISOString = (isoString: string | null): Date | null => {
  if (!isoString) return null
  return new Date(isoString)
}

/**
 * Creates a TZDate object with the configured timezone
 * @param date The date to convert
 * @returns TZDate object with the configured timezone
 */
export const toTZDate = (date: Date | string): TZDate => {
  if (date instanceof Date) {
    return TZDate.tz(config.time.timezone, date)
  } else {
    return TZDate.tz(config.time.timezone, date)
  }
}

/**
 * Formats a date to a specific format in the configured timezone
 * @param date The date to format
 * @param formatString The format string to use
 * @returns Formatted date string
 */
export const formatInTimezone = (
  date: Date | string | null,
  formatString: string = 'yyyy-MM-dd HH:mm:ss'
): string => {
  if (!date) return ''
  // Handle different types correctly
  const tzDate =
    date instanceof Date
      ? TZDate.tz(config.time.timezone, date)
      : TZDate.tz(config.time.timezone, date as string)
  return format(tzDate, formatString, { in: tz(config.time.timezone) })
}

/**
 * Formats a time in 12-hour format (hh:mm a)
 * @param date The date to format
 * @returns Time string in 12-hour format
 */
export const formatTime12h = (date: Date | string | null): string => {
  if (!date) return ''
  return formatInTimezone(date, 'hh:mm a')
}

/**
 * Formats a date in yyyy-MM-dd format
 * @param date The date to format
 * @returns Date string in yyyy-MM-dd format
 */
export const formatDateOnly = (date: Date | string | null): string => {
  if (!date) return ''
  return formatInTimezone(date, 'yyyy-MM-dd')
}

/**
 * Checks if the first date is before the second date
 * @param date1 The first date
 * @param date2 The second date
 * @returns True if date1 is before date2
 */
export const isDateBefore = (
  date1: Date | string | null,
  date2: Date | string | null
): boolean => {
  if (!date1 || !date2) return false
  // Parse and normalize to UTC
  const d1 = typeof date1 === 'string' ? parseISO(date1) : date1
  const d2 = typeof date2 === 'string' ? parseISO(date2) : date2
  return isBefore(d1, d2)
}

/**
 * Checks if the first date is after the second date
 * @param date1 The first date
 * @param date2 The second date
 * @returns True if date1 is after date2
 */
export const isDateAfter = (
  date1: Date | string | null,
  date2: Date | string | null
): boolean => {
  if (!date1 || !date2) return false
  // Parse and normalize to UTC
  const d1 = typeof date1 === 'string' ? parseISO(date1) : date1
  const d2 = typeof date2 === 'string' ? parseISO(date2) : date2

  return isAfter(d1, d2)
}

/**
 * Parses a time string in 12-hour format (hh:mm a) to a Date object
 * @param timeString The time string to parse
 * @param referenceDate The reference date to use
 * @returns Date object
 */
export const parseTime12h = (
  timeString: string | null,
  referenceDate: Date = new Date()
): Date | null => {
  if (!timeString) return null
  try {
    // Use the tz option from date-fns instead of timeZone
    return parse(timeString, 'hh:mm a', referenceDate, {
      locale: undefined,
      weekStartsOn: 0,
      firstWeekContainsDate: 1,
      useAdditionalWeekYearTokens: false,
      useAdditionalDayOfYearTokens: false,
      in: tz(config.time.timezone),
    })
  } catch (error) {
    return null
  }
}

/**
 * Converts a day index (0-6) to its name
 * @param dayIndex The day index (0=Sunday, 1=Monday, ..., 6=Saturday)
 * @returns The name of the day
 */
export function getDayName(dayIndex: number | string): string {
  const days = [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
  ]

  const index = typeof dayIndex === 'string' ? parseInt(dayIndex, 10) : dayIndex
  return days[index] || 'Unknown'
}
