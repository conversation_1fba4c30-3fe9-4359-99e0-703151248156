import { dbFirebase } from '@/firebase/dbFirebase'
import logger from '@/lib/logger'
import { TQueueSummary } from '@/types/queueSummary.types'
import { db } from '@/database/db'
import { tempQueues } from '@/database/schema'
import { and, eq, gte, sql } from 'drizzle-orm'
import { doc, setDoc } from 'firebase/firestore'
import { thresholds } from './update-firebase.service'
import * as dateUtils from '@/utils/dateFormat.utils'
import { getCachedAverageDwellTimes } from './queue-metrics/queries/dwell-time.query'

// Local cache to store the latest queueSummary data
let cachedQueueSummary: TQueueSummary | null = null

export const updateQueueSummaryFirebase = async () => {
  const AverageEntryTime = await getAverageEntryTime()
  const AverageServiceTime = await getAverageServiceTime()

  if (!thresholds || thresholds.targetEntryTime === undefined) {
    logger.warn(
      'Thresholds or target entry times are missing. Using default values.'
    )
  }

  const PercentageEntryTime = await getPercentageEntryTime(
    thresholds?.targetEntryTime ?? 120
  )

  const newQueueSummary: TQueueSummary = {
    avgEntryTime: {
      value: AverageEntryTime,
      percentage: PercentageEntryTime,
    },
    avgServiceTime: {
      value: AverageServiceTime,
    },
    tenantId: 'clove',
    updatedAt: new Date(),
  }

  // Compare with local cache
  if (
    cachedQueueSummary &&
    cachedQueueSummary.avgEntryTime.value ===
      newQueueSummary.avgEntryTime.value &&
    cachedQueueSummary.avgEntryTime.percentage ===
      newQueueSummary.avgEntryTime.percentage &&
    cachedQueueSummary.avgServiceTime.value ===
      newQueueSummary.avgServiceTime.value
  ) {
    logger.debug('No changes in queue summary, skipping update.')
    return
  }

  // Update Firebase and local cache
  logger.info('Updating queue summary', { queueSummary: newQueueSummary })
  const updateResult = await updateQueueSummary(newQueueSummary)

  if (updateResult.success) {
    cachedQueueSummary = newQueueSummary // Save to local cache
  } else {
    logger.error('Failed to update queue summary:', updateResult.error)
  }
}

export const resetQueueSummaryFirebase = async () => {
  const newQueueSummary: TQueueSummary = {
    avgEntryTime: {
      value: 0,
      percentage: null,
    },
    avgServiceTime: {
      value: 0,
    },
    tenantId: 'clove',
    updatedAt: new Date(),
  }

  logger.info('Resetting queue summary', { queueSummary: newQueueSummary })

  const resetResult = await updateQueueSummary(newQueueSummary)

  if (resetResult.success) {
    cachedQueueSummary = newQueueSummary // Update local cache with reset values
    logger.info('Queue summary reset successfully.')
  } else {
    logger.error('Failed to reset sections metrics:', resetResult.error)
  }
}

async function getAverageEntryTime() {
  const thirtyMinutesAgo = dateUtils.getRollingWindowStart(30)

  const result = await db
    .select({
      averageDwellTime: sql<number>`ROUND(AVG(${tempQueues.dur}))::INT`.as(
        'average_dwell_time'
      ),
    })
    .from(tempQueues)
    .where(
      and(
        gte(tempQueues.ts, thirtyMinutesAgo),
        eq(tempQueues.evt_type, 'q_exit_resto')
      )
    )

  return result[0]?.averageDwellTime ?? 0
}

async function getAverageServiceTime() {
  const { q1AverageDwellTime, q2AverageDwellTime } =
    getCachedAverageDwellTimes()
  return q1AverageDwellTime + q2AverageDwellTime
}

async function getPercentageEntryTime(
  targetTime: number
): Promise<number | null> {
  const thirtyMinutesAgo = dateUtils.getRollingWindowStart(30)

  // Query to count entries below the target time
  const belowTargetResult = await db
    .select({
      count: sql<string>`COUNT(*)`.as('entries_below_target'),
    })
    .from(tempQueues)
    .where(
      and(
        gte(tempQueues.ts, thirtyMinutesAgo),
        eq(tempQueues.evt_type, 'q_exit_resto'),
        sql`${tempQueues.dur} < ${targetTime}` // Filter entries below target time
      )
    )

  const belowTargetCount = parseInt(belowTargetResult[0]?.count ?? '0', 10)

  // Query to count total entries
  const totalResult = await db
    .select({
      count: sql<string>`COUNT(*)`.as('total_entries'),
    })
    .from(tempQueues)
    .where(
      and(
        gte(tempQueues.ts, thirtyMinutesAgo),
        eq(tempQueues.evt_type, 'q_exit_resto')
      )
    )

  const totalCount = parseInt(totalResult[0]?.count ?? '0', 10)

  // Calculate percentage
  if (totalCount === 0) {
    return null // Avoid division by zero and return null
  }

  const percentage = (belowTargetCount / totalCount) * 100
  return Math.round(percentage) // Return rounded percentage
}

export const updateQueueSummary = async (
  metricsData: TQueueSummary,
  documentId: string = 'clove'
): Promise<{
  success: boolean
  error: string | null
}> => {
  try {
    const metricsRef = doc(dbFirebase, 'queueSummaries', documentId)

    await setDoc(metricsRef, metricsData, { merge: true })

    return {
      success: true,
      error: null,
    }
  } catch (error) {
    logger.error('Error updating queue summary:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    }
  }
}
