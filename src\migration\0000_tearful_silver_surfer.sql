CREATE TABLE "events" (
	"id" uuid DEFAULT gen_random_uuid(),
	"evt_type" text NOT NULL,
	"cam" text NOT NULL,
	"ts" timestamp with time zone NOT NULL,
	"t_id" text,
	"q_name" text,
	"loc" text NOT NULL,
	"dur" integer NOT NULL,
	"count" integer NOT NULL,
	"tenant_id" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "temp_queues" (
	"id" uuid DEFAULT gen_random_uuid(),
	"evt_type" text NOT NULL,
	"cam" text NOT NULL,
	"ts" timestamp with time zone NOT NULL,
	"t_id" text,
	"q_name" text,
	"loc" text NOT NULL,
	"dur" integer NOT NULL,
	"count" integer NOT NULL,
	"tenant_id" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE INDEX "ts_idx" ON "temp_queues" USING btree ("ts");