import { EventData, EventType } from '@/types/eventData.types'
import logger from '@/lib/logger'
import { config } from '@/config'
import * as dateUtils from '@/utils/dateFormat.utils'
import { updateOverallEntry, updatePeakQueue } from '../document.service'
import { debounce } from 'lodash'
import {
  TPendingPeakQueueUpdates,
  TPendingStoreUpdates,
  TStoreMetricsState,
} from '../types'

// In-memory state for store-wide metrics
const storeMetrics: TStoreMetricsState = {
  restaurantEntries: 0,
  kopiEntries: 0,
  overallEntry: 0,
  peakQueue1: { count: 0, timestamp: null },
  latestQueue1Ts: null,
}

// Pending updates for debouncing
let pendingStoreUpdates: TPendingStoreUpdates = {
  restaurantEntries: 0,
  kopiEntries: 0,
  overallEntry: 0,
}
let pendingPeakQueueUpdates: TPendingPeakQueueUpdates = {
  peakQueue1: { count: 0, timestamp: null },
}

// Debounce settings
const DEBOUNCE_WAIT_MS = config.debounce.delayMs
const DEBOUNCE_MAX_WAIT_MS = config.debounce.maxWaitMs

// Debounced update function for store entry metrics
const debouncedUpdateStoreMetrics = debounce(
  async () => {
    // Take a snapshot and reset immediately
    const updatesToProcess = { ...pendingStoreUpdates }
    pendingStoreUpdates = {
      restaurantEntries: 0,
      kopiEntries: 0,
      overallEntry: 0,
    }
    try {
      await updateStoreMetricsToFirestore(updatesToProcess)
    } catch (err) {
      logger.error('Error in debouncedUpdateStoreMetrics:', { error: err })
    }
  },
  DEBOUNCE_WAIT_MS,
  { maxWait: DEBOUNCE_MAX_WAIT_MS }
)

// Debounced update function for peak queue metrics (separate to handle rapid events)
const debouncedUpdatePeakQueue = debounce(
  async () => {
    try {
      await updatePeakQueueToFirestore()
      // Reset pending peak queue updates
      pendingPeakQueueUpdates = {
        peakQueue1: { count: 0, timestamp: null },
      }
    } catch (err) {
      logger.error('Error in debouncedUpdatePeakQueue:', { error: err })
    }
  },
  DEBOUNCE_WAIT_MS,
  { maxWait: DEBOUNCE_MAX_WAIT_MS }
)

// Function to update store metrics to Firestore
const updateStoreMetricsToFirestore = async (
  updatesSnapshot: TPendingStoreUpdates
) => {
  try {
    // Calculate the net change in overall entry since last update
    const netChange =
      updatesSnapshot.restaurantEntries + updatesSnapshot.kopiEntries

    logger.info('Updating Firestore with net change', {
      netChange,
      updatesSnapshot,
    })
    if (netChange !== 0) {
      await updateOverallEntry(netChange)
    }

    // logger.info('Store metrics update to firebase triggered', { netChange })
  } catch (err) {
    logger.error('Error updating store metrics to Firestore:', { error: err })
  }
}

// Function to update peak queue to Firestore
const updatePeakQueueToFirestore = async () => {
  try {
    if (pendingPeakQueueUpdates.peakQueue1.count > 0) {
      await updatePeakQueue(
        storeMetrics.peakQueue1.count,
        storeMetrics.peakQueue1.timestamp
      )
    }
  } catch (err) {
    logger.error('Error updating peak queue metrics to Firestore:', {
      error: err,
    })
  }
}

// Main handler function for store-wide metrics
export async function handleStoreEvent(eventData: EventData) {
  try {
    // logger.info('Received event for processing', {
    //   eventType: eventData.evt_type,
    //   eventData,
    // })
    // Handle Overall Entry calculations
    if (
      eventData.evt_type === EventType.RESTO_ENTRY ||
      eventData.evt_type === EventType.Q_EXIT_RESTO
    ) {
      // logger.info('Processing restaurant entry event', {
      //   eventType: eventData.evt_type,
      //   eventData,
      //   storeMetrics,
      // })
      // Update store metrics
      storeMetrics.restaurantEntries++
      storeMetrics.overallEntry =
        storeMetrics.restaurantEntries + storeMetrics.kopiEntries

      // Update pending store updates
      pendingStoreUpdates.restaurantEntries++
      pendingStoreUpdates.overallEntry =
        pendingStoreUpdates.restaurantEntries + pendingStoreUpdates.kopiEntries
      // logger.debug('Restaurant entry event processed', {
      //   eventType: eventData.evt_type,
      //   pendingStoreUpdates,
      //   storeMetrics,
      // })
    } else if (eventData.evt_type === EventType.KOPI_ENTRY) {
      // logger.debug('Processing Kopitiam entry event', {
      //   eventType: eventData.evt_type,
      //   eventData,
      //   storeMetrics,
      // })
      // Update store metrics
      storeMetrics.kopiEntries++
      storeMetrics.overallEntry =
        storeMetrics.restaurantEntries + storeMetrics.kopiEntries
      // Update pending store updates
      pendingStoreUpdates.kopiEntries++
      pendingStoreUpdates.overallEntry =
        pendingStoreUpdates.restaurantEntries + pendingStoreUpdates.kopiEntries
      // logger.debug('Kopitiam entry event processed', {
      //   eventType: eventData.evt_type,
      //   pendingStoreUpdates,
      //   storeMetrics,
      // })
    } else if (eventData.evt_type === EventType.DU_KOPI_ENTRY) {
      // logger.debug('Processing duplicate Kopitiam entry event', {
      //   eventType: eventData.evt_type,
      //   eventData,
      //   storeMetrics,
      // })
      // Adjust for duplicate Kopitiam entries when received
      const duplicateCount = eventData.count || 0
      storeMetrics.kopiEntries -= duplicateCount
      storeMetrics.overallEntry =
        storeMetrics.restaurantEntries + storeMetrics.kopiEntries
      // Update pending store updates
      pendingStoreUpdates.kopiEntries -= duplicateCount
      pendingStoreUpdates.overallEntry =
        pendingStoreUpdates.restaurantEntries + pendingStoreUpdates.kopiEntries
      // logger.debug('Duplicate Kopitiam entry event processed', {
      //   eventType: eventData.evt_type,
      //   pendingStoreUpdates,
      //   storeMetrics,
      //   duplicateCount,
      // })
      // logger.info('Adjusted for duplicate Kopitiam entries', { duplicateCount })
    }

    // Trigger debounced update once after processing any store entry event
    if (
      eventData.evt_type === EventType.RESTO_ENTRY ||
      eventData.evt_type === EventType.Q_EXIT_RESTO ||
      eventData.evt_type === EventType.KOPI_ENTRY ||
      eventData.evt_type === EventType.DU_KOPI_ENTRY
    ) {
      debouncedUpdateStoreMetrics()
    }

    // Handle Peak Queue calculations for count_state events
    if ((eventData.evt_type as string) === 'count_state') {
      // logger.info('Processing count state event', {
      //   eventType: eventData.evt_type,
      //   eventData,
      //   storeMetrics,
      // })
      const eventTs = eventData.ts
      if (eventData.q_name === 'q1' && eventData.loc === 'q1') {
        // Check if this event is newer than the latest processed for Queue 1
        if (
          !storeMetrics.latestQueue1Ts ||
          dateUtils.isDateAfter(eventTs, storeMetrics.latestQueue1Ts)
        ) {
          // Update latest timestamp
          storeMetrics.latestQueue1Ts = eventTs
          // Update peak queue metrics if current count is higher
          if (eventData.count > storeMetrics.peakQueue1.count) {
            storeMetrics.peakQueue1.count = eventData.count
            storeMetrics.peakQueue1.timestamp = eventTs
            pendingPeakQueueUpdates.peakQueue1.count = eventData.count
            pendingPeakQueueUpdates.peakQueue1.timestamp = eventTs
            // Trigger debounced update for peak queue
            debouncedUpdatePeakQueue()
          }
        }
      }
    }
  } catch (err) {
    logger.error('Error handling store event:', { error: err, eventData })
  }
}
