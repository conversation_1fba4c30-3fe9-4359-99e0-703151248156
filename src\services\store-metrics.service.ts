import { dbFirebase } from '@/firebase/dbFirebase'
import logger from '@/lib/logger'
import { TStoreMetrics } from '@/types/storeMetrics.types'
import { db } from '@/database/db'
import { events } from '@/database/schema'
import { and, eq, gte, inArray, sql } from 'drizzle-orm'
import { doc, setDoc } from 'firebase/firestore'
import * as dateUtils from '@/utils/dateFormat.utils'

// Local cache to store the latest storeMetrics data
let cachedStoreMetrics: TStoreMetrics | null = null

/**
 * DEPRECATED: This function is no longer needed as updates are handled in real-time by store-event.handler.ts
 */
export const updateStoreMetricsFirebase = async () => {
  const totalEntriesLast30Min = await getTotalEntriesLast30Min()
  const peakQueue = await getPeakQueue()
  const totalEntries = await getTotalEntries()

  const newStoreMetrics: TStoreMetrics = {
    entryStats: {
      last30m: totalEntriesLast30Min,
      overall: totalEntries,
      peak: {
        time: peakQueue.timestamp ? peakQueue.timestamp.toISOString() : '',
        value: peakQueue.count,
      },
    },
    tenantId: 'clove',
    updatedAt: new Date(),
  }

  // Compare with local cache
  if (
    cachedStoreMetrics &&
    cachedStoreMetrics.entryStats.last30m ===
      newStoreMetrics.entryStats.last30m &&
    cachedStoreMetrics.entryStats.overall ===
      newStoreMetrics.entryStats.overall &&
    cachedStoreMetrics.entryStats.peak.value ===
      newStoreMetrics.entryStats.peak.value
  ) {
    logger.debug('No changes in store metrics, skipping update.')
    return
  }

  // Update Firebase and local cache
  logger.info('Updating store metrics', { storeMetrics: newStoreMetrics })
  const updateResult = await updateStoreMetrics(newStoreMetrics)

  if (updateResult.success) {
    cachedStoreMetrics = newStoreMetrics // Save to local cache
  }
}

/**
 * DEPRECATED: This function is no longer needed as total entries are tracked in real-time by store-event.handler.ts
 */
async function getTotalEntries() {
  const startOfDay = new Date(new Date().setHours(0, 0, 0, 0))

  const result = await db
    .select({
      count: sql<string>`count(*)`.as('entry_count'),
    })
    .from(events)
    .where(
      and(
        gte(events.ts, startOfDay),
        inArray(events.evt_type, ['q_exit_resto', 'kopi_entry', 'resto_entry'])
      )
    )

  return parseInt(result[0]?.count ?? '0', 10)
}

/**
 * DEPRECATED: This function is no longer needed as total entries are tracked in real-time by store-event.handler.ts
 */
async function getTotalEntriesLast30Min(): Promise<number> {
  const now = dateUtils.toTZDate(new Date())
  const thirtyMinutesAgo = dateUtils.toTZDate(
    new Date(now.getTime() - 30 * 60 * 1000)
  )

  const result = await db
    .select({
      entry_count: sql<string>`count(*)`.as('entry_count'),
    })
    .from(events)
    .where(
      and(
        gte(events.ts, thirtyMinutesAgo),
        inArray(events.evt_type, ['q_exit_resto', 'kopi_entry', 'resto_entry'])
      )
    )

  return parseInt(result[0]?.entry_count ?? '0', 10)
}

/**
 * DEPRECATED: This function is no longer needed as peak queue is updated in real-time by store-event.handler.ts
 */
async function getPeakQueue() {
  const now = dateUtils.toTZDate(new Date())
  const startOfDay = dateUtils.toTZDate(
    new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0, 0)
  )

  const result = await db
    .select({
      count: events.count,
      timestamp: events.ts,
    })
    .from(events)
    .where(and(gte(events.ts, startOfDay), eq(events.evt_type, 'count_state')))
    .orderBy(sql`${events.count} DESC`)
    .limit(1)

  return {
    count: result[0]?.count ?? 0,
    timestamp: result[0]?.timestamp ?? null,
  }
}

export const updateStoreMetrics = async (
  metricsData: TStoreMetrics,
  documentId: string = 'clove'
): Promise<{
  success: boolean
  error: string | null
}> => {
  try {
    const metricsRef = doc(dbFirebase, 'storeMetrics', documentId)

    await setDoc(metricsRef, metricsData, { merge: true })

    return {
      success: true,
      error: null,
    }
  } catch (error) {
    logger.error('Error updating store metrics:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    }
  }
}
