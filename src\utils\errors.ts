/**
 * Type definition for an application error
 */
export type ApplicationError = {
  message: string
  code: string
  cause: Error | null
  name: 'ApplicationError'
  stack?: string
  [key: string]: unknown // Allow additional properties (e.g., uid, tenantId)
}

/**
 * Type for error definition with code and message
 */
export type ErrorDefinition = {
  code: string
  message: string
}

/**
 * Predefined application error codes and messages
 * Use with appError for consistent error handling
 */
export const ERRORS = {
  FIREBASE: {
    AUTH: {
      code: 'FIREBASE_AUTH_ERROR',
      message: 'Failed to authenticate with Firebase',
    },
    LISTENER: {
      code: 'FIREBASE_LISTENER_ERROR',
      message: 'Failed to setup Firebase listeners',
    },
    FIRESTORE: {
      code: 'FIRESTORE_CONNECTION_ERROR',
      message: 'Failed to connect to Firestore',
    },
  },
  DATABASE: {
    CONNECTION: {
      code: 'DATABASE_CONNECTION_ERROR',
      message: 'Failed to establish database connection',
    },
  },
  WHATSAPP: {
    INIT: {
      code: 'WHATSAPP_INIT_ERROR',
      message: 'Failed to initialize WhatsApp service',
    },
    SHUTDOWN: {
      code: 'WHATSAPP_SHUTDOWN_ERROR',
      message: 'Failed to shutdown WhatsApp service',
    },
  },
  RABBITMQ: {
    START: {
      code: 'RABBITMQ_START_ERROR',
      message: 'Failed to start RabbitMQ receiver',
    },
    STOP: {
      code: 'RABBITMQ_STOP_ERROR',
      message: 'Failed to stop RabbitMQ receiver',
    },
  },
  JOBS: {
    CLEANUP: {
      code: 'JOB_CLEANUP_ERROR',
      message: 'Failed to execute job cleanup function',
    },
    OPERATING_HOURS: {
      code: 'OPERATING_HOURS_UPDATE_ERROR',
      message: 'Failed to update jobs from operating hours',
    },
  },
  SHUTDOWN: {
    PARTIAL: {
      code: 'PARTIAL_SHUTDOWN_ERROR',
      message: 'Shutdown completed with cleanup errors',
    },
  },
} as const

export const appError = (
  errorDef: { code: string; message: string },
  context?: string | Error | null,
  extra: Record<string, unknown> = {}
): ApplicationError => {
  const baseMessage = errorDef.message
  const cause = context instanceof Error ? context : null
  const contextMsg = typeof context === 'string' ? context : cause?.message

  return {
    name: 'ApplicationError',
    code: errorDef.code,
    message: contextMsg ? `${baseMessage}: ${contextMsg}` : baseMessage,
    cause,
    stack: cause?.stack ?? new Error().stack,
    ...extra,
  }
}
