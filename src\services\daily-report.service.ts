import { queueHourlySummary, events } from '@/database/schema'
import { eq, sql } from 'drizzle-orm'
import { db } from '@/database/db'
import logger from '@/lib/logger'

/**
 * Generates the daily report after operational hours
 * This will populate daily_metrics and queue_hourly_summary tables
 */
export async function generateDailyReport(): Promise<void> {
  const today = new Date().toISOString().split('T')[0]
  logger.info(`Starting daily report generation for ${today}`)

  try {
    // Generate daily metrics for each location
    // await Promise.all([
    //   generateDailyMetricsForLocation(today, 'restaurant'),
    //   generateDailyMetricsForLocation(today, 'kopitiam'),
    //   generateDailyMetricsForLocation(today, 'elevators'),
    // ])

    // await generateDailyMetricsForLocation(today, 'restaurant')

    // Generate hourly queue summaries
    await generateQueueHalfHourlySummary(today)

    logger.info(`Daily report generation for ${today} completed successfully`)
  } catch (error) {
    logger.error(`Failed to generate daily report for ${today}`, {
      error: (error as Error).message,
    })
    throw error
  }
}

// async function generateDailyMetricsForLocation(
//   date: string,
//   location: string
// ): Promise<void> {
//   logger.info(`Generating daily metrics for ${location} on ${date}`)

//   try {
//     const eventTypes = {
//       restaurant: ['resto_entry', 'q_exit_resto'],
//       kopitiam: ['kopi_entry'],
//       elevators: ['elev_exit'],
//     }

//     const selectedEventTypes =
//       eventTypes[location as keyof typeof eventTypes] || []

//     const startOfDay = new Date(new Date(date).setHours(0, 0, 0, 0))
//     const endOfDay = new Date(new Date(date).setHours(23, 59, 59, 999))

//     // Check if record already exists
//     const existingRecord = await db
//       .select()
//       .from(dailyMetrics)
//       .where(and(eq(dailyMetrics.date, date), eq(dailyMetrics.loc, location)))
//       .limit(1)

//     // Calculate first entry time based on event type specific to location
//     const [firstEntryTimeResult] = await db
//       .select({
//         time: sql`MIN(${events.ts})::time`,
//       })
//       .from(events)
//       .where(
//         and(
//           between(events.ts, startOfDay, endOfDay),
//           inArray(events.evt_type, selectedEventTypes)
//         )
//       )

//     // Calculate peak entry - count max people in any 30 min slot from 00:00 to 23:59
//     const slotTimeExpression = sql`
//       date_trunc('hour', ${events.ts}) +
//       (INTERVAL '30 minute' * (EXTRACT(minute FROM ${events.ts})::integer / 30))
//     `
//     const timeSlotsCTE = db
//       .select({
//         slotTime: slotTimeExpression.as('slot_time'),
//         entryCount: count(events.id).as('entry_count'),
//       })
//       .from(events)
//       .where(
//         and(
//           between(events.ts, startOfDay, endOfDay),
//           inArray(events.evt_type, selectedEventTypes)
//         )
//       )
//       .groupBy(slotTimeExpression)
//       .as('time_slots')

//     const [peakEntryResult] = await db
//       .with(timeSlotsCTE)
//       .select({
//         peak: sql`MAX(${timeSlotsCTE.entryCount})`,
//       })
//       .from(timeSlotsCTE)

//     // Calculate total count - count row of the event type
//     const [totalCountResult] = await db
//       .select({
//         total: count(events.id),
//       })
//       .from(events)
//       .where(
//         and(
//           between(events.ts, startOfDay, endOfDay),
//           inArray(events.evt_type, selectedEventTypes)
//         )
//       )

//     // Calculate additional metrics based on location
//     let exitedElevators = 0
//     let toRestaurant = 0
//     let toKopitiam = 0
//     let reKopitiam = 0
//     let reRestaurant = 0
//     let toMall = 0
//     let fromElevatorsToQueue = 0
//     let overallEntry = 0

//     if (location === 'elevators') {
//       // People who exited from elevators
//       const [exitedElevatorsResult] = await db
//         .select({ count: count(events.id) })
//         .from(events)
//         .where(
//           and(
//             between(events.ts, startOfDay, endOfDay),
//             eq(events.evt_type, 'elev_exit')
//           )
//         )

//       // Extract the count, defaulting to 0 if no result
//       exitedElevators = exitedElevatorsResult?.count || 0

//       // People who left for mall from elevators
//       // TODO: Need to ask david about the logic of this
//       toMall = 0

//       // People who entered queue from elevators
//       // TODO: Need to ask david about the logic of this
//       fromElevatorsToQueue = 0
//     }

//     if (location === 'restaurant') {
//       // People who exited queue 2 and entered into restaurant + direct entries
//       const [toRestaurantResult] = await db
//         .select({ count: sum(events.count) })
//         .from(events)
//         .where(
//           and(
//             between(events.ts, startOfDay, endOfDay),
//             inArray(events.evt_type, ['q_exit_resto', 'resto_entry'])
//           )
//         )

//       toRestaurant = Number(toRestaurantResult?.count || 0)

//       // count re-entries by t_id
//       // i think the cam 4 and 5 have same t_id,how about other camera, how to know this re entry? TODO: ask david
//       reRestaurant = 0

//       // Total entry of restaurant (direct entries + from queue + kopitiam)
//       const overallEntryResult = await db.execute(sql`
//           SELECT SUM(${events.count}) as count
//           FROM ${events}
//           WHERE ${events.ts} BETWEEN ${startOfDay} AND ${endOfDay}
//           AND ${events.evt_type} IN ('resto_entry', 'q_exit_resto', 'kopi_entry')
//         `)
//       overallEntry = parseInt(
//         (overallEntryResult.rows[0]?.count as string) || '0',
//         10
//       )
//     }

//     if (location === 'kopitiam') {
//       // is this need to call du_kopi_entry or calculate from events table? TODO: ask david
//       // currently, we are using events table to calculate the kopi entry
//       // People who entered kopitiam
//       const kopiEntryResult = await db.execute(sql`
//           SELECT SUM(${events.count}) as count
//           FROM ${events}
//           WHERE ${events.ts} BETWEEN ${startOfDay} AND ${endOfDay}
//           AND ${events.evt_type} = 'kopi_entry'
//         `)
//       toKopitiam = parseInt(
//         (kopiEntryResult.rows[0]?.count as string) || '0',
//         10
//       )

//       // Count re-entries by t_id
//       // todo : ask david about the logic of this
//       reKopitiam = 0

//       // For kopitiam, overall entry is the same as total entries
//       overallEntry = toKopitiam
//     }

//     const metrics = {
//       date: new Date(date).toISOString().split('T')[0], // Convert to string in ISO format
//       loc: location,
//       firstEntryTime: firstEntryTimeResult.time as string,
//       peakEntry: peakEntryResult.peak as number,
//       totalCount: totalCountResult.total as number,
//       exitedElevators,
//       toRestaurant,
//       toKopitiam,
//       reKopitiam,
//       reRestaurant,
//       toMall,
//       fromElevatorsToQueue,
//       overallEntry,
//       lastUpdated: new Date(),
//     }

//     if (existingRecord.length > 0) {
//       // Update existing record
//       await db
//         .update(dailyMetrics)
//         .set(metrics)
//         .where(eq(dailyMetrics.id, existingRecord[0].id))
//       logger.info(`Updated daily metrics for ${location} on ${date}`)
//     } else {
//       // Insert new record
//       await db.insert(dailyMetrics).values(metrics)
//       logger.info(`Inserted new daily metrics for ${location} on ${date}`)
//     }

//     logger.info(
//       `Successfully generated daily metrics for ${location} on ${date}`
//     )
//   } catch (error) {
//     logger.error(`Error generating daily metrics for ${location} on ${date}`, {
//       error: (error as Error).message,
//     })
//     throw error
//   }
// }

async function generateQueueHalfHourlySummary(date: string): Promise<void> {
  logger.info(`Generating half-hourly queue summary for ${date}`)

  for (let hour = 0; hour < 24; hour++) {
    for (let half = 0; half < 2; half++) {
      const hourFormatted = hour.toString().padStart(2, '0')
      const minuteFormatted = (half * 30).toString().padStart(2, '0')
      const halfHourStart = new Date(
        `${date}T${hourFormatted}:${minuteFormatted}:00`
      )
      const halfHourEnd = new Date(
        `${date}T${hourFormatted}:${minuteFormatted === '00' ? '29:59' : '59:59'}`
      )
      halfHourEnd.setMilliseconds(999)

      try {
        const hasDataResult = await db.execute(sql`
          SELECT EXISTS(
            SELECT 1 
            FROM ${events}
            WHERE ${events.ts} BETWEEN ${halfHourStart} AND ${halfHourEnd}
            AND ${events.evt_type} IN ('q_entry', 'q_exit', 'q_exit_resto')
            LIMIT 1
          ) as exists
        `)

        if (hasDataResult.rows[0]?.exists === false) {
          continue
        }

        const existingRecord = await db
          .select()
          .from(queueHourlySummary)
          .where(eq(queueHourlySummary.timestampHour, halfHourStart))
          .limit(1)

        // Calculate metrics for queue 1
        const q1CountResult = await db.execute(sql`
          SELECT COUNT(${events.id}) as count
          FROM ${events}
          WHERE ${events.ts} BETWEEN ${halfHourStart} AND ${halfHourEnd}
          AND ${events.evt_type} = 'q_entry'
          AND ${events.q_name} = 'q1'
        `)

        const q1AvgDwellResult = await db.execute(sql`
          SELECT ROUND(AVG(${events.dur}) / 60.0, 1)::REAL as avg_dwell
          FROM ${events}
          WHERE ${events.q_name} = 'q1'
          AND ${events.ts} BETWEEN ${halfHourStart} AND ${halfHourEnd}
          AND ${events.evt_type} IN ('q_exit')
        `)

        // Calculate metrics for queue 2
        const q2CountResult = await db.execute(sql`
          SELECT COUNT(${events.id}) as count
          FROM ${events}
          WHERE ${events.ts} BETWEEN ${halfHourStart} AND ${halfHourEnd}
          AND ${events.evt_type} = 'q_entry'
          AND ${events.q_name} = 'q2'
        `)

        const q2AvgDwellResult = await db.execute(sql`
          SELECT ROUND(AVG(${events.dur}) / 60.0, 1)::REAL as avg_dwell
          FROM ${events}
          WHERE ${events.q_name} = 'q2'
          AND ${events.ts} BETWEEN ${halfHourStart} AND ${halfHourEnd}
          AND ${events.evt_type} IN ('q_exit', 'q_exit_resto')
        `)

        const q1AbandonResult = await db.execute(sql`
          SELECT COUNT(${events.id}) as count
          FROM ${events}
          WHERE ${events.ts} BETWEEN ${halfHourStart} AND ${halfHourEnd}
          AND ${events.evt_type} = 'q_abandon'
          AND ${events.q_name} = 'q1'
        `)

        const halfHourlyData = {
          timestampHour: halfHourStart,
          q1TotalCount: parseInt(
            (q1CountResult.rows[0]?.count as string) || '0',
            10
          ),
          q1AvgDwellTime: parseFloat(
            parseFloat(
              (q1AvgDwellResult.rows[0]?.avg_dwell as string) || '0'
            ).toFixed(1)
          ), // Ensure float with 1 decimal
          q2TotalCount: parseInt(
            (q2CountResult.rows[0]?.count as string) || '0',
            10
          ),
          q2AvgDwellTime: parseFloat(
            parseFloat(
              (q2AvgDwellResult.rows[0]?.avg_dwell as string) || '0'
            ).toFixed(1)
          ), // Ensure float with 1 decimal
          q1Abandon: parseInt(
            (q1AbandonResult.rows[0]?.count as string) || '0',
            10
          ),
          updatedAt: new Date(),
        }

        if (existingRecord.length > 0) {
          // Update existing record
          await db
            .update(queueHourlySummary)
            .set(halfHourlyData)
            .where(eq(queueHourlySummary.id, existingRecord[0].id))
          logger.info(
            `Updated queue half-hourly summary for ${date} ${hourFormatted}:${minuteFormatted}`
          )
        } else {
          // Insert new record
          await db.insert(queueHourlySummary).values({
            ...halfHourlyData,
          })
          logger.info(
            `Inserted new queue half-hourly summary for ${date} ${hourFormatted}:${minuteFormatted}`
          )
        }
      } catch (error) {
        logger.error(
          `Error generating queue summary for ${hourFormatted}:${minuteFormatted} on ${date}`,
          { error: (error as Error).message }
        )
        throw error
      }
    }
  }

  logger.info(`Successfully generated half-hourly queue summary for ${date}`)
}
