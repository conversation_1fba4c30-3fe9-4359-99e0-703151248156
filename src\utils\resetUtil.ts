import { dbFirebase } from '@/firebase/dbFirebase'
import logger from '@/lib/logger'
import { doc, setDoc } from 'firebase/firestore'

/**
 * Generic function to reset a collection document
 * @param collectionName The name of the collection to reset
 * @param documentId The document ID to reset
 * @param resetData The data to reset the document with
 * @returns Promise<void>
 */
export async function resetCollectionDocument(
  collectionName: string,
  documentId: string,
  resetData: Record<string, any>
): Promise<void> {
  try {
    // Always ensure updatedAt is current
    resetData.updatedAt = null

    const docRef = doc(dbFirebase, collectionName, documentId)
    await setDoc(docRef, resetData)
    logger.info(
      `Successfully reset ${collectionName} document for ${documentId}`
    )
  } catch (error) {
    logger.error(`Error resetting ${collectionName}:`, error)
    throw error
  }
}
