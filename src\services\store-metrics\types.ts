// Type definitions for store metrics state
export type TStoreMetricsState = {
  restaurantEntries: number
  kopiEntries: number
  overallEntry: number
  peakQueue1: {
    count: number
    timestamp: Date | null
  }
  latestQueue1Ts: Date | null
}

export type TPendingStoreUpdates = {
  restaurantEntries: number
  kopiEntries: number
  overallEntry: number
}

export type TPendingPeakQueueUpdates = {
  peakQueue1: {
    count: number
    timestamp: Date | null
  }
}
