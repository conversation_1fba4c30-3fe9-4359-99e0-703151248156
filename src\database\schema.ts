import {
  integer,
  pgTable,
  text,
  timestamp,
  uuid,
  index,
  serial,
  date,
  varchar,
  time,
  real,
} from 'drizzle-orm/pg-core'

export const events = pgTable('events', {
  id: uuid('id').defaultRandom(),
  evt_type: text('evt_type').notNull(),
  cam: text('cam').notNull(),
  ts: timestamp('ts', { withTimezone: true }).notNull(),
  t_id: text('t_id'),
  q_name: text('q_name'),
  loc: text('loc').notNull(),
  dur: integer('dur').notNull(),
  count: integer('count').notNull(),
  tenant_id: text('tenant_id').notNull(),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
})

export const tempQueues = pgTable(
  'temp_queues',
  {
    id: uuid('id').defaultRandom(),
    evt_type: text('evt_type').notNull(),
    cam: text('cam').notNull(),
    ts: timestamp('ts', { withTimezone: true }).notNull(),
    t_id: text('t_id'),
    q_name: text('q_name'),
    loc: text('loc').notNull(),
    dur: integer('dur').notNull(),
    count: integer('count').notNull(),
    tenant_id: text('tenant_id').notNull(),
    created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
  },
  (table) => [index('ts_idx').on(table.ts)]
)

export const dailyMetrics = pgTable('daily_metrics', {
  id: serial('id').primaryKey(),
  date: date('date').notNull(),
  loc: varchar('loc', { length: 50 }).notNull(),
  firstEntryTime: time('first_entry_time', { withTimezone: true }),
  peakEntry: integer('peak_entry'),
  totalCount: integer('total_count'),
  exitedElevators: integer('exited_elevators'),
  toRestaurant: integer('to_restaurant'),
  toKopitiam: integer('to_kopitiam'),
  reKopitiam: integer('re_kopitiam'),
  reRestaurant: integer('re_restaurant'),
  toMall: integer('to_mall'),
  fromElevatorsToQueue: integer('from_elevators_to_queue'),
  overallEntry: integer('overall_entry'),
  lastUpdated: timestamp('last_updated', { withTimezone: true }),
})

export const queueHourlySummary = pgTable('queue_hourly_summary', {
  id: serial('id').primaryKey(),
  timestampHour: timestamp('timestamp_hour', { withTimezone: true }).notNull(),
  q1TotalCount: integer('q1_total_count'),
  q1AvgDwellTime: real('q1_avg_dwell_time'),
  q2TotalCount: integer('q2_total_count'),
  q2AvgDwellTime: real('q2_avg_dwell_time'),
  q1Abandon: integer('q1_abandon'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }),
})
