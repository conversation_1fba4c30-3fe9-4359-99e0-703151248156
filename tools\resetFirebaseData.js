#!/usr/bin/env node
const { initializeApp } = require('firebase/app')
const { getFirestore, doc, setDoc } = require('firebase/firestore')
const readline = require('readline')

// Firebase configurations
const firebaseConfigs = require('./resetFireBaseConfig.json')

// Import reset data configurations
const {
  sectionsMetricsData,
  storeMetricsData,
  queueSummariesData,
  queueMetricsData,
} = require('./dataConfig')

// CLI Interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
})

// Function to reset a collection document
async function resetCollectionDocument(
  db,
  collectionName,
  documentId,
  resetData
) {
  try {
    // Always ensure updatedAt is current
    resetData.updatedAt = null

    const docRef = doc(db, collectionName, documentId)
    await setDoc(docRef, resetData)
    console.log(
      `✓ Successfully reset ${collectionName} document for ${documentId}`
    )
  } catch (error) {
    console.error(`Error resetting ${collectionName}:`, error)
    throw error
  }
}

// Main reset function
async function resetAllData(environmentKey) {
  try {
    console.log(
      `\nInitializing Firebase with ${environmentKey} configuration...`
    )

    // Initialize Firebase with selected config
    const app = initializeApp(firebaseConfigs[environmentKey])
    const db = getFirestore(app)

    console.log('\nStarting reset of all collections...')

    // Reset metrics collections
    await resetCollectionDocument(
      db,
      'sectionsMetrics',
      'clove',
      sectionsMetricsData
    )
    await resetCollectionDocument(db, 'storeMetrics', 'clove', storeMetricsData)
    await resetCollectionDocument(
      db,
      'queueSummaries',
      'clove',
      queueSummariesData
    )
    await resetCollectionDocument(db, 'queueMetrics', 'clove', queueMetricsData)

    console.log('\nAll collections successfully reset!')
    return true
  } catch (error) {
    console.error('\nError in reset all data script:', error)
    return false
  }
}

function displayMenu() {
  console.clear()
  console.log('=================================')
  console.log('   FIREBASE DATA RESET UTILITY   ')
  console.log('=================================\n')
  console.log('Select Firebase environment:')
  console.log('1. Development')
  console.log('2. Staging')
  console.log('3. Production')
  console.log('4. Exit\n')

  rl.question('Enter your choice (1-4): ', async (answer) => {
    switch (answer) {
      case '1':
        console.log(
          '\n⚠️  WARNING: You are about to reset data in the DEVELOPMENT environment'
        )
        confirmReset('dev')
        break
      case '2':
        console.log(
          '\n⚠️  WARNING: You are about to reset data in the STAGING environment'
        )
        confirmReset('staging')
        break
      case '3':
        console.log(
          '\n⚠️  WARNING: You are about to reset data in the PRODUCTION environment'
        )
        console.log('⚠️  THIS WILL AFFECT LIVE DATA! BE CAREFUL!')
        confirmReset('production')
        break
      case '4':
        console.log('\nExiting program')
        rl.close()
        break
      default:
        console.log('\nInvalid option. Please try again.')
        setTimeout(displayMenu, 1500)
        break
    }
  })
}

function confirmReset(env) {
  rl.question(
    `\nAre you sure you want to proceed with ${env.toUpperCase()} reset? (y/n): `,
    async (confirmation) => {
      if (confirmation.toLowerCase() === 'y') {
        console.log(`\nResetting ${env.toUpperCase()} data...`)

        try {
          const success = await resetAllData(env)
          if (success) {
            console.log('\nReset complete! Exiting...')
            // Exit with success code
            process.exit(0)
          } else {
            console.error('\nFailed to reset data. Exiting...')
            // Exit with error code
            process.exit(1)
          }
        } catch (error) {
          console.error('\nFailed to reset data:', error)
          // Exit with error code
          process.exit(1)
        }
      } else {
        console.log('\nReset cancelled.')
        setTimeout(displayMenu, 1500)
      }
    }
  )
}

// Start the application
displayMenu()
