import { Client } from 'pg'
import { env } from '../src/config/env'

async function truncateEvents() {
  const client = new Client({
    host: env.POSTGRES_HOST,
    port: env.POSTGRES_PORT,
    user: env.POSTGRES_USER,
    password: env.POSTGRES_PASSWORD,
    database: env.POSTGRES_DATABASE,
  })

  try {
    await client.connect()
    console.log('Connected to database')

    // Truncate events tables
    await client.query('TRUNCATE TABLE events')
    console.log('Successfully truncated events tables')

    process.exit(0)
  } catch (error) {
    console.error('Error truncating tables:', error)
    process.exit(1)
  } finally {
    await client.end()
  }
}

truncateEvents()
