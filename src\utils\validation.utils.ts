export function isValidId(id: string): boolean {
  return typeof id === 'string' && id.length > 0
}

export function isValidString(value: unknown): value is string {
  return typeof value === 'string' && value.trim().length > 0
}

export function sanitizeString(value: string): string {
  return value.trim()
}

export class ValidationError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'ValidationError'
  }
}
