import { dbFirebase } from '@/firebase/dbFirebase'
import logger from '@/lib/logger'
import { TQueueMetrics } from '@/types/queueMetrics.types'
import { db } from '@/database/db'
import { events, tempQueues } from '@/database/schema'
import { and, desc, eq, gte, sql } from 'drizzle-orm'
import { doc, setDoc } from 'firebase/firestore'
import { checkQueueThresholds } from './queue-notification.service'

// Local cache to store the latest queueMetrics data
let cachedQueueMetrics: TQueueMetrics | null = null

export const updateQueueMetricsFirebase = async () => {
  const q1Count = await getLatestQ1Count()
  const q2Count = await getLatestQ2Count()
  const q1AverageDwellTime = await getQ1AverageDwellTime()
  const q2AverageDwellTime = await getQ2AverageDwellTime()

  // Check queue thresholds dynamically
  checkQueueThresholds(q1Count)

  const newQueueMetrics: TQueueMetrics = {
    entryQueue: {
      inQueue: q1Count,
      avgDwellTime: q1AverageDwellTime,
    },
    seatingQueue: {
      inQueue: q2Count,
      avgDwellTime: q2AverageDwellTime,
    },
    tenantId: 'clove',
    updatedAt: new Date(),
  }

  // Compare with local cache
  if (
    cachedQueueMetrics &&
    cachedQueueMetrics.entryQueue.inQueue ===
      newQueueMetrics.entryQueue.inQueue &&
    cachedQueueMetrics.entryQueue.avgDwellTime ===
      newQueueMetrics.entryQueue.avgDwellTime &&
    cachedQueueMetrics.seatingQueue.inQueue ===
      newQueueMetrics.seatingQueue.inQueue &&
    cachedQueueMetrics.seatingQueue.avgDwellTime ===
      newQueueMetrics.seatingQueue.avgDwellTime
  ) {
    logger.debug('No changes in queue metrics, skipping update.')
    return
  }

  // Update Firebase and local cache
  logger.info('Updating queue metrics', { queueMetrics: newQueueMetrics })
  const updateResult = await updateQueueMetrics(newQueueMetrics)

  if (updateResult.success) {
    cachedQueueMetrics = newQueueMetrics // Save to local cache
  } else {
    logger.error('Failed to update queue metrics:', updateResult.error)
  }
}

async function getLatestQ1Count() {
  // Add 30 minutes to the current time for the grace period
  const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000)

  const [latestEvent] = await db
    .select()
    .from(events)
    .where(
      and(
        eq(events.evt_type, 'count_state'),
        eq(events.q_name, 'q1'),
        gte(events.ts, thirtyMinutesAgo)
      )
    )
    .orderBy(desc(events.ts))
    .limit(1)

  return latestEvent?.count || 0
}

async function getLatestQ2Count() {
  // Add 30 minutes to the current time for the grace period
  const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000)

  const [latestEvent] = await db
    .select()
    .from(events)
    .where(
      and(
        eq(events.evt_type, 'count_state'),
        eq(events.q_name, 'q2'),
        gte(events.ts, thirtyMinutesAgo)
      )
    )
    .orderBy(desc(events.ts))
    .limit(1)

  return latestEvent?.count || 0
}

async function getQ1AverageDwellTime() {
  const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000)

  const result = await db
    .select({
      averageDwellTime: sql<number>`ROUND(AVG(${tempQueues.dur}))::INT`.as(
        'average_dwell_time'
      ),
    })
    .from(tempQueues)
    .where(
      sql`
                ${tempQueues.q_name} = 'q1'
                AND ${tempQueues.ts} >= ${thirtyMinutesAgo}
                AND ${tempQueues.evt_type} IN ('q_exit')
            `
    )

  return result[0]?.averageDwellTime ?? 0
}

async function getQ2AverageDwellTime() {
  const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000)

  const result = await db
    .select({
      averageDwellTime: sql<number>`ROUND(AVG(${tempQueues.dur}))::INT`.as(
        'average_dwell_time'
      ),
    })
    .from(tempQueues)
    .where(
      sql`
                ${tempQueues.q_name} = 'q2'
                AND ${tempQueues.ts} >= ${thirtyMinutesAgo}
                AND ${tempQueues.evt_type} IN ('q_exit', 'q_exit_resto')
            `
    )

  return result[0]?.averageDwellTime ?? 0
}

export const updateQueueMetrics = async (
  metricsData: TQueueMetrics,
  documentId: string = 'clove'
): Promise<{
  success: boolean
  error: string | null
}> => {
  try {
    const metricsRef = doc(dbFirebase, 'queueMetrics', documentId)

    await setDoc(metricsRef, metricsData, { merge: true })

    return {
      success: true,
      error: null,
    }
  } catch (error) {
    logger.error('Error updating queue metrics:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    }
  }
}
