import { dbFirebase } from '@/firebase/dbFirebase'
import logger from '@/lib/logger'
import {
  TOperatingHours,
  DayIndex,
  TimeString,
  TStore,
  TOperatingHoursWithTimeZone,
} from '@/types/store.types'
import { TThresholds } from '@/types/thresholds.types'
import { collection, onSnapshot } from 'firebase/firestore'
import { TZDate } from '@date-fns/tz/date'
import { config } from '@/config'
import { getDayName } from '@/utils/dateFormat.utils'

export let operatingHours: TOperatingHours | null = null
export let thresholds: TThresholds | null = null
let store: TStore | null = null

/**
 * Parses a time string in various formats (HH:MM, H:MM) into hour and minute components.
 * Handles both single and double-digit hour formats.
 */
function parseTimeString(timeString: TimeString): {
  hour: number
  minute: number
} {
  // Handle various time formats
  const parts = timeString.split(':')

  if (parts.length !== 2) {
    throw new Error(
      `Invalid time format: ${timeString}. Expected format: HH:MM or H:MM`
    )
  }

  const hour = parseInt(parts[0], 10)
  const minute = parseInt(parts[1], 10)

  // Validate parsed values
  if (
    isNaN(hour) ||
    isNaN(minute) ||
    hour < 0 ||
    hour > 23 ||
    minute < 0 ||
    minute > 59
  ) {
    throw new Error(
      `Invalid time values in: ${timeString}. Hour must be 0-23, minute must be 0-59`
    )
  }

  return { hour, minute }
}

/**
 * Sets up listeners for both `store` (operatingHours) and `thresholds` collections.
 * Calls the provided callbacks when data changes.
 */
export const setupFirebaseListeners = (
  onOperatingHoursUpdate: (hours: TOperatingHours | null) => void
): (() => void) => {
  // Listener for store (operatingHours)
  const storeRef = collection(dbFirebase, 'stores')
  const unsubscribeStore = onSnapshot(
    storeRef,
    (snapshot) => {
      snapshot.forEach((doc) => {
        const storeData = doc.data()
        store = storeData as TStore
        operatingHours = storeData.operatingHours as TOperatingHours
        logger.info('Operating Hours Updated:', operatingHours)
        onOperatingHoursUpdate(operatingHours)
      })
    },
    (error) => {
      logger.error('Error fetching operating hours from store:', {
        error: error.message,
      })
    }
  )

  // Listener for thresholds
  const thresholdsRef = collection(dbFirebase, 'thresholds')
  const unsubscribeThresholds = onSnapshot(
    thresholdsRef,
    (snapshot) => {
      snapshot.forEach((doc) => {
        thresholds = doc.data() as TThresholds
        logger.info('Thresholds Updated:', thresholds)
      })
    },
    (error) => {
      logger.error('Error fetching thresholds:', {
        error: error.message,
      })
    }
  )

  // Return a function to unsubscribe both listeners
  return () => {
    unsubscribeStore()
    unsubscribeThresholds()
  }
}

/**
 * Checks if the current time is within operational hours, considering timezone and buffer.
 * Uses date-fns and @date-fns/tz for proper timezone handling.
 */
export const isWithinOperationalHours = (): boolean => {
  if (!operatingHours) {
    logger.warn('Operational hours data from firebase not available yet.')
    return false
  }

  const bufferMinutes = config.time.operationalHours.bufferMinutes
  const timezone = config.time.timezone // Use the configured timezone

  // Get current date in the configured timezone
  const now = new TZDate(new Date(), timezone)
  const currentDay = now.getDay() as DayIndex // 0 = Sunday, 1 = Monday, etc.

  const todaySchedule = operatingHours[currentDay]

  // If today is closed, return false
  if (todaySchedule.isClosed) {
    logger.info(`Today (${currentDay}) is marked as closed.`)
    return false
  }

  // If open or close is null (should not happen if isClosed is false, but we check for safety)
  if (!todaySchedule.open || !todaySchedule.close) {
    logger.warn(
      `Invalid schedule for day ${currentDay}: open or close time is null`
    )
    return false
  }

  // Parse open and close times and create TZDate objects in the configured timezone
  try {
    const { hour: openHour, minute: openMinute } = parseTimeString(
      todaySchedule.open
    )
    const { hour: closeHour, minute: closeMinute } = parseTimeString(
      todaySchedule.close
    )

    // Create TZDate objects for open and close times in configured timezone
    const openTime = TZDate.tz(
      timezone,
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      openHour,
      openMinute
    )

    const closeTime = TZDate.tz(
      timezone,
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      closeHour,
      closeMinute
    )

    // Apply buffer to open and close times
    const adjustedOpenTime = new TZDate(
      openTime.getTime() - bufferMinutes * 60 * 1000,
      timezone
    )
    const adjustedCloseTime = new TZDate(
      closeTime.getTime() + bufferMinutes * 60 * 1000,
      timezone
    )

    // Check if current time is within the adjusted operational hours
    const isWithinHours = now >= adjustedOpenTime && now <= adjustedCloseTime

    // Log detailed information for debugging
    logger.debug(`Operational hours check: ${isWithinHours}`, {
      timezone,
      currentTime: now.toISOString(),
      openTime: openTime.toISOString(),
      closeTime: closeTime.toISOString(),
      adjustedOpenTime: adjustedOpenTime.toISOString(),
      adjustedCloseTime: adjustedCloseTime.toISOString(),
      bufferMinutes,
    })

    return isWithinHours
  } catch (error) {
    logger.error(
      `Error parsing operational hours: ${error instanceof Error ? error.message : String(error)}`,
      {
        day: currentDay,
        openTime: todaySchedule.open,
        closeTime: todaySchedule.close,
      }
    )
    return false
  }
}

/**
 * Gets the operating hours with buffer times applied.
 * Returns the operating hours structure in the same format as the original,
 * but with adjusted times to include buffer windows.
 *
 * @returns The operating hours with buffer times applied or null if data is unavailable
 */
export const getOperatingHours = (): TOperatingHoursWithTimeZone | null => {
  if (!operatingHours) {
    logger.warn('Operational hours data from firebase not available yet.')
    return null
  }

  if (!store) {
    logger.warn('Store data from firebase not available yet.')
    return null
  }

  const bufferMinutes = config.time.operationalHours.bufferMinutes
  const storeTimezone = store.timeZone || config.time.timezone

  // Create a deep copy of the original operating hours
  const result = JSON.parse(JSON.stringify(operatingHours)) as TOperatingHours

  // Initialize as the base type first
  const resultWithTimeZone: TOperatingHoursWithTimeZone =
    {} as TOperatingHoursWithTimeZone

  // Add timezone to each day
  ;(Object.keys(result).map(Number) as DayIndex[]).forEach((dayIndex) => {
    // Create the day entry with timezone added
    resultWithTimeZone[dayIndex] = {
      ...result[dayIndex],
      timeZone: storeTimezone,
    }

    const daySchedule = resultWithTimeZone[dayIndex]

    // Store original values for proper logging
    const originalOpen = daySchedule.open
    const originalClose = daySchedule.close

    // Skip if the day is closed or missing time data
    if (daySchedule.isClosed || !originalOpen || !originalClose) {
      return
    }

    try {
      // Parse the original times
      const { hour: openHour, minute: openMinute } =
        parseTimeString(originalOpen)
      const { hour: closeHour, minute: closeMinute } =
        parseTimeString(originalClose)

      // Calculate buffer-adjusted times in minutes since midnight
      let openMinutesSinceMidnight = openHour * 60 + openMinute
      let closeMinutesSinceMidnight = closeHour * 60 + closeMinute

      // Apply buffer (subtract from open, add to close)
      openMinutesSinceMidnight = Math.max(
        0,
        openMinutesSinceMidnight - bufferMinutes
      )
      closeMinutesSinceMidnight = Math.min(
        24 * 60 - 1,
        closeMinutesSinceMidnight + bufferMinutes
      )

      // Convert back to hours and minutes
      const adjustedOpenHour = Math.floor(openMinutesSinceMidnight / 60)
      const adjustedOpenMinute = openMinutesSinceMidnight % 60
      const adjustedCloseHour = Math.floor(closeMinutesSinceMidnight / 60)
      const adjustedCloseMinute = closeMinutesSinceMidnight % 60

      // Format the times back to strings
      const adjustedOpen = `${adjustedOpenHour.toString().padStart(2, '0')}:${adjustedOpenMinute.toString().padStart(2, '0')}`
      const adjustedClose = `${adjustedCloseHour.toString().padStart(2, '0')}:${adjustedCloseMinute.toString().padStart(2, '0')}`

      resultWithTimeZone[dayIndex].open = adjustedOpen
      resultWithTimeZone[dayIndex].close = adjustedClose

      logger.debug(
        `Applied buffer to operating hours for day ${dayIndex} (${getDayName(dayIndex)})`,
        {
          originalOpen,
          originalClose,
          adjustedOpen,
          adjustedClose,
          bufferMinutes,
          timeZone: storeTimezone,
        }
      )
    } catch (error) {
      logger.error(
        `Error adjusting operational hours for day ${dayIndex} (${getDayName(dayIndex)}) with buffer: ${error instanceof Error ? error.message : String(error)}`,
        {
          originalOpen,
          originalClose,
          bufferMinutes,
        }
      )
      // Keep the original values if there's an error
    }
  })

  return resultWithTimeZone
}
