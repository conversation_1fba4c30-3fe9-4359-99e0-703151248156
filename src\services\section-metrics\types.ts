export type TSectionEntry = {
  firstEntry: Date | null
  totalEntry: number
  peakEntry: number
  peakTime?: Date | null
}

export type TElevators = {
  totalCount: number
}

export type TSectionsMetrics = {
  tenantId: string
  resto: TSectionEntry
  kopitiam: TSectionEntry
  elevators: TElevators
  updatedAt: Date | null
}

export type TPeakSlot = {
  peakTime: Date | null
  peakCount: number
}

export const Section = {
  ELEVATORS: 'elevators',
  KOPITIAM: 'kopitiam',
  RESTO: 'resto',
} as const

export type TSection = (typeof Section)[keyof typeof Section]

// New types for better type safety
export type TSectionKey = keyof typeof Section
export type TPeakSectionKey = Extract<TSectionKey, 'KOPITIAM' | 'RESTO'> // Only KOPITIAM and RESTO have peak data

// Define peak section values for better type safety
export const PEAK_SECTION_VALUES = ['kopitiam', 'resto'] as const
export type TPeakSectionValue = (typeof PEAK_SECTION_VALUES)[number]

export type TPeakData = {
  [key in TPeakSectionValue]: TPeakSlot
}

// Helper type for building peak update payloads
export type TPeakEntryUpdate = {
  'kopitiam.peakEntry': number
  'resto.peakEntry': number
}

export type TPeakTimeUpdate = {
  'kopitiam.peakTime': Date | null
  'resto.peakTime': Date | null
}

export type TPeakUpdates = Partial<TPeakEntryUpdate & TPeakTimeUpdate>
