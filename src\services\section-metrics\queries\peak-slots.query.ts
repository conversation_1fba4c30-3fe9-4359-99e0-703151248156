import { config } from '@/config'
import { db } from '@/database/db'
import { events } from '@/database/schema'
import { EventType } from '@/types/eventData.types'
import * as dateUtils from '@/utils/dateFormat.utils'
import { sql } from 'drizzle-orm'

import { Section, type TPeakData } from '../types'
import logger from '@/lib/logger'

export async function getPeakSlots(
  timeSlotMinutes = config.time.timeSlotMinutes
): Promise<TPeakData> {
  try {
    const now = dateUtils.toTZDate(new Date())
    const startOfDay = dateUtils.toTZDate(
      new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0, 0)
    )

    const result = await db.execute(sql`
      WITH time_slots AS (
        SELECT
          CASE
            WHEN ${events.evt_type} = ${EventType.KOPI_ENTRY} THEN ${Section.KOPITIAM}
            WHEN ${events.evt_type} IN (${EventType.RESTO_ENTRY}, ${EventType.Q_EXIT_RESTO}) THEN ${Section.RESTO}
          END AS section,
          date_trunc('hour', ${events.ts}) +
          (INTERVAL '1 minute' * (${timeSlotMinutes} * (EXTRACT(minute FROM ${events.ts})::integer / ${timeSlotMinutes}))) AS slot_time,
          COUNT(*) AS entry_count
        FROM ${events}
        WHERE
          ${events.ts} >= ${startOfDay}
          AND ${events.evt_type} IN (${EventType.KOPI_ENTRY}, ${EventType.RESTO_ENTRY}, ${EventType.Q_EXIT_RESTO})
        GROUP BY section, slot_time
      ),
      ranked_slots AS (
        SELECT
          section,
          slot_time,
          entry_count,
          ROW_NUMBER() OVER (PARTITION BY section ORDER BY entry_count DESC, slot_time ASC) AS rank
        FROM time_slots
      )
      SELECT
        section,
        slot_time AS peak_time,
        entry_count AS peak_count
      FROM ranked_slots
      WHERE rank = 1
    `)

    const peakData: TPeakData = {
      [Section.KOPITIAM]: { peakTime: null, peakCount: 0 },
      [Section.RESTO]: { peakTime: null, peakCount: 0 },
    }

    for (const row of result.rows) {
      const section = row.section
      if (section === Section.KOPITIAM || section === Section.RESTO) {
        peakData[section] = {
          peakTime: row.peak_time ? (row.peak_time as Date) : null,
          peakCount: Number(row.peak_count ?? 0),
        }
      }
    }

    return peakData
  } catch (err) {
    logger.error('Error fetching peak slots:', { error: err })
    throw err
  }
}
