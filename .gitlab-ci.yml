variables:
  SERVER_HOST_NAME: root
  DESTINATION: 'opt/clove'
  # PM2_NAME: 'test-cicd'
  NODE_ENV: production
  GIT_DEPTH: 0
  NPM_CONFIG_LOGLEVEL: warn

stages:
  - test_access
  - lint
  - build
  - versioning
  - deploy
  - sync

default:
  tags:
    - server-30
    - shell
    - ubuntu

# ------------------------------
# Lint
# ------------------------------
lint:
  stage: lint
  script:
    - echo "Running job in $CI_JOB_STAGE stage"
    - node --version
    - npm --version
    - NODE_ENV=development npm ci # Use `ci` for faster and reliable installs
    - npm run lint
  only:
    - develop
    - main
    - merge_requests

# ------------------------------
# Build
# ------------------------------

build:
  stage: build
  script:
    - echo "Starting build process..."
    - node --version
    - npm --version
    - NODE_ENV=development npm ci # Ensures devDependencies are installed
    - npm run build
    - echo "Listing directory contents after build:"
    - ls -la
    - echo "Listing dist directory contents:"
    - ls -la dist/
  artifacts:
    paths:
      - dist/
      - package.json
      - package-lock.json
      - ecosystem.config.js
    expire_in: 1 day # Longer expiration for deployment jobs
  only:
    - develop
    - main

# ------------------------------
# Versioning (Semantic Release)
# ------------------------------
versioning:
  stage: versioning
  image: node:20
  before_script:
    - git config --global user.name "GitLab CI"
    - git config --global user.email "<EMAIL>"
    - npm install --no-save --no-package-lock semantic-release @semantic-release/commit-analyzer @semantic-release/release-notes-generator @semantic-release/changelog @semantic-release/git @semantic-release/gitlab
  script:
    - echo "Setting up Git authentication for semantic-release"
    - export GITLAB_TOKEN=${GL_TOKEN}
    - git remote set-url origin "https://gitlab-ci:${GITLAB_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git"
    - git remote -v
    - |
      if [[ "$CI_COMMIT_BRANCH" == "develop" ]]; then
        echo "Dry-running semantic-release on develop"
        npx semantic-release --dry-run --debug
      else
        echo "Running full semantic-release on $CI_COMMIT_BRANCH"
        npx semantic-release --debug
      fi
    - VERSION=$(git describe --tags --abbrev=0 || echo "none")
    - echo "SEMANTIC_VERSION=$VERSION" >> build.env
  artifacts:
    reports:
      dotenv: build.env
  only:
    - main
    - develop
  needs:
    - build

# ------------------------------
# Access Server Template
# ------------------------------
.access:
  variables:
    SSH_KEY: ''
    SERVER_HOST_IP: ''
  before_script: |
    echo "===> Setting up environment <==="
    eval $(ssh-agent -s)
    chmod 400 "$SSH_KEY"
    ssh-add "$SSH_KEY"
  script: |
    echo "===> Accessing $SERVER_HOST_IP <==="
    ssh -o StrictHostKeyChecking=no -i "$SSH_KEY" "$SERVER_HOST_NAME@$SERVER_HOST_IP" "ls -la"
    echo "===> DONE <==="

# ------------------------------
# Test access to server
# ------------------------------
test_access_to_staging:
  stage: test_access
  extends:
    - .access
  variables:
    SSH_KEY: $SSH_PRIVATE_KEY_STAGING
    SERVER_HOST_IP: $SERVER_HOST_IP_OFFICE
  only:
    - merge_requests
  when: on_success

test_access_to_production:
  stage: test_access
  extends:
    - .access
  variables:
    SSH_KEY: $SSH_PRIVATE_KEY_PROD
    SERVER_HOST_IP: $SERVER_HOST_IP_PROD
  needs:
    - test_access_to_staging
  only:
    - merge_requests
  when: on_success

# ------------------------------
# Deploy Template
# ------------------------------
.deploy:
  variables:
    DEPLOY_ENV: ''
    SSH_KEY: ''
    APP_NAME: ''
    ENV_FILE: ''
    SERVER_HOST_IP: ''
    PG_CA_CERT: ''
    FIREBASE_KEY: ''
  before_script: |
    echo "Checking environment variables..."
    echo "DEPLOY_ENV: $DEPLOY_ENV"
    echo "SERVER_HOST_IP: $SERVER_HOST_IP"
    echo "SERVER_HOST_NAME: $SERVER_HOST_NAME"
    echo "DESTINATION: $DESTINATION"
    echo "SEMANTIC_VERSION: $SEMANTIC_VERSION"

    echo "===> Setting up environment <==="
    echo "$ENV_FILE" > ".env"
    chmod 755 ".env"

    if [ -n "$PG_CA_CERT" ]; then
      echo "===> Extracting PG cert for $DEPLOY_ENV <==="
      mkdir -p certs
      cp "$PG_CA_CERT" certs/pg-ca.crt
      chmod 400 certs/pg-ca.crt
    fi

    if [ -n "$FIREBASE_KEY" ]; then
      echo "===> Extracting Firebase service key for $DEPLOY_ENV <==="
      cp "$FIREBASE_KEY" serviceAccountKey.json
      chmod 400 serviceAccountKey.json
    fi

    eval $(ssh-agent -s)
    chmod 400 "$SSH_KEY"
    ssh-add "$SSH_KEY"

  script: |
    echo "===> Accessing $SERVER_HOST_IP <==="
    ssh -o StrictHostKeyChecking=no -i "$SSH_KEY" "$SERVER_HOST_NAME@$SERVER_HOST_IP" "ls -la"

    echo "===> Deploying to $SERVER_HOST_IP <==="
    ssh -o StrictHostKeyChecking=no -i "$SSH_KEY" "$SERVER_HOST_NAME@$SERVER_HOST_IP" "mkdir -p /$DESTINATION"
    scp -o StrictHostKeyChecking=no -i "$SSH_KEY" -r package*.json dist "$SERVER_HOST_NAME@$SERVER_HOST_IP:/$DESTINATION"
    scp -o StrictHostKeyChecking=no -i "$SSH_KEY" ".env" "$SERVER_HOST_NAME@$SERVER_HOST_IP:/$DESTINATION/.env"
    if [ -n "$PG_CA_CERT" ]; then
      ssh -o StrictHostKeyChecking=no -i "$SSH_KEY" "$SERVER_HOST_NAME@$SERVER_HOST_IP" "mkdir -p /$DESTINATION/certs"
      scp -o StrictHostKeyChecking=no -i "$SSH_KEY" "certs/pg-ca.crt" "$SERVER_HOST_NAME@$SERVER_HOST_IP:/$DESTINATION/certs/pg-ca.crt"
    fi
    if [ -n "$FIREBASE_KEY" ]; then
      scp -o StrictHostKeyChecking=no -i "$SSH_KEY" "serviceAccountKey.json" "$SERVER_HOST_NAME@$SERVER_HOST_IP:/$DESTINATION/serviceAccountKey.json"
    fi
    scp -o StrictHostKeyChecking=no -i "$SSH_KEY" ecosystem.config.js "$SERVER_HOST_NAME@$SERVER_HOST_IP:/$DESTINATION/ecosystem.config.js"
    ssh -o StrictHostKeyChecking=no -i "$SSH_KEY" "$SERVER_HOST_NAME@$SERVER_HOST_IP" "cd /$DESTINATION && npm ci --production"

    echo "✅ Deployed version: $SEMANTIC_VERSION"
    ssh -o StrictHostKeyChecking=no -i "$SSH_KEY" "$SERVER_HOST_NAME@$SERVER_HOST_IP" "cd /$DESTINATION && pm2 reload ecosystem.config.js || pm2 start ecosystem.config.js"

# ------------------------------
# Deploy to Dev
# ------------------------------
deploy_to_develop:
  stage: deploy
  extends: .deploy
  variables:
    DEPLOY_ENV: staging
    SSH_KEY: $SSH_PRIVATE_KEY_STAGING
    SERVER_HOST_IP: $SERVER_HOST_IP_OFFICE
    ENV_FILE: $ENV_STAGING_FILE
    FIREBASE_KEY: $FIREBASE_STAGING_PRIVATE_KEY
  only:
    - develop
  when: on_success
  environment:
    name: staging
  needs:
    - build
    - versioning

# ------------------------------
# Deploy to Production
# ------------------------------
deploy_to_production:
  stage: deploy
  extends: .deploy
  variables:
    DEPLOY_ENV: production
    SSH_KEY: $SSH_PRIVATE_KEY_PROD
    SERVER_HOST_IP: $SERVER_HOST_IP_PROD
    ENV_FILE: $ENV_PROD_FILE
    PG_CA_CERT: $PG_CA_CERT_FILE
    FIREBASE_KEY: $FIREBASE_PROD_PRIVATE_KEY
  only:
    - main
  environment:
    name: production
  when: manual
  needs:
    - build
    - versioning

# ------------------------------
# Post Deploy Sync (main → develop)
# ------------------------------
sync_main_to_develop:
  stage: sync
  script:
    - echo "🔄 Merging main into develop with [skip ci]"
    - git remote set-url origin "https://gitlab-ci:${GL_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git"
    - chmod +x .gitlab/sync-main-to-develop.sh
    - .gitlab/sync-main-to-develop.sh
  only:
    - main
  when: on_success
  needs:
    - deploy_to_production
