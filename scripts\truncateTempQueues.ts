import { Client } from 'pg'
import { env } from '../src/config/env'

export async function truncateTempQueues() {
  const client = new Client({
    host: env.POSTGRES_HOST,
    port: env.POSTGRES_PORT,
    user: env.POSTGRES_USER,
    password: env.POSTGRES_PASSWORD,
    database: env.POSTGRES_DATABASE,
  })

  try {
    await client.connect()
    console.log('Connected to database')

    // Truncate temp_queues tables
    await client.query('TRUNCATE TABLE temp_queues')
    console.log('Successfully truncated temp_queues tables')

    process.exit(0)
  } catch (error) {
    console.error('Error truncating tables:', error)
    process.exit(1)
  } finally {
    await client.end()
  }
}

truncateTempQueues()
