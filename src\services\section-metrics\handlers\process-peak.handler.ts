import logger from '@/lib/logger'

import {
  ensureDocumentExists,
  updateMetricsDocument,
} from '../document.service'
import { getPeakSlots } from '../queries/peak-slots.query'
import {
  PEAK_SECTION_VALUES,
  TPeakSectionValue,
  TPeakData,
  TPeakSlot,
  TPeakUpdates,
} from '../types'

import { toISOString } from '@/utils/dateFormat.utils'

let lastPeakData: TPeakData | null = null

const hasPeakDataChanged = (current: TPeakData): boolean => {
  if (!lastPeakData) {
    return true
  }

  const previous: TPeakData = lastPeakData

  const dateToString = (d: Date | null) => (d ? toISOString(d) : '')

  return PEAK_SECTION_VALUES.some((section: TPeakSectionValue) => {
    const currentSlot: TPeakSlot = current[section]
    const lastSlot: TPeakSlot = previous[section]

    const currentTime = currentSlot.peakTime
    const lastTime = lastSlot.peakTime

    return (
      currentSlot.peakCount !== lastSlot.peakCount ||
      dateToString(currentTime) !== dateToString(lastTime)
    )
  })
}

export const processPeak = async (): Promise<void> => {
  try {
    // logger.debug('Starting Sections Metrics update at:', {
    //   time: dateUtils.toISOString(new Date()),
    // })

    const doc = await ensureDocumentExists()
    if (!doc) {
      logger.warn('Skipping count update - document not found')
      return
    }

    const peakData: TPeakData = await getPeakSlots()

    if (!hasPeakDataChanged(peakData)) {
      logger.debug('No changes in peakData, skipping update')
      return
    }

    const updates: TPeakUpdates = {}

    // Build updates object using the typed PeakSectionValue
    for (const section of PEAK_SECTION_VALUES) {
      updates[`${section}.peakEntry`] = peakData[section].peakCount
      updates[`${section}.peakTime`] = peakData[section].peakTime
        ? new Date(peakData[section].peakTime)
        : null
    }

    await updateMetricsDocument(updates)
    lastPeakData = peakData
  } catch (err) {
    logger.error('Error updating Sections Metrics:', { error: err })
    throw err
  }
}
