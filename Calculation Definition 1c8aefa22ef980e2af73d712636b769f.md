# Calculation Definition

### **Definitions:**

### 🏬 **To Mall ( calculate after ops hour for report )**

The total number of people who exited the elevators **but did not** enter the restaurant or the Kopitiam, adjusted for repeated entries to the Kopitiam.

```json
To Mall =
  Total Exited Elevators -
  (Total Entered Restaurant + Final Total Entered Kopitiam)

  Final Total Kopitiam = Total Event of enter_kopitiam - Du_Enter_Kopitiam


Note: Repeated Kopitiam Entries value will be given by end of the operaring hour.
```

### 🧍‍♂️ From Elevators to Queue (**calculate after ops hour for report)**

The total number of people who exited the elevators **but didn’t** go to the Mall (i.e., they joined the restaurant queue).

```json
From Elevators to Queue =
  Total Exiting Elevators − To Mall
```

### **To Restaurant**

Total number of people entering the restaurant.

```json
To Restaurant = Total entering the restaurant from Queue 02 + total entering the restaurant directly without queuing."
```

### **To Kopitiam**

```json
Final Total Kopitiam = Total count of entered Kopitiam - Duplicate Kopitiam Entry
```

**Overall Entry** = Total Entered restaurant + Total Entered Kopitiam

**Average Service Tim**e - 30 mins window = Average Dwelling time of Q1 + Average Dwelling time of Q2

![image.png](image.png)

**Average Entry Time** - 30 mins window : From Queue 2 to Restaurant ( q_exit_resto) only.

**Threshold Color**

### 🎯 **Queue-to-Entry Duration Thresholds**

**Performance is measured against a strict target:**

**Note: This threshold Color also use to apply for Avg Dwell Time of Entry Queue and Seating Queue**

> Target Time: ≤ 2 minutes (120 seconds) from queuing to entering the restaurant.

---

### 📊 **Threshold Breakdown:**

| Duration (seconds) | Duration (minutes) | Color     | Interpretation                          |
| ------------------ | ------------------ | --------- | --------------------------------------- |
| **0 – 59**         | < 1 minute         | 🟢 Green  | Very fast — optimal queue performance.  |
| **60 – 120**       | 1 to 2 minutes     | 🟡 Yellow | Acceptable — meets the 2-minute target. |
| **> 120**          | > 2 minutes        | 🔴 Red    | Delayed — exceeds the 2-minute target.  |

![image.png](image%201.png)

**Description**:
At each interval, calculate the percentage of people who entered the restaurant within 2 minutes of queuing, based on `q_exit_resto` events recorded in the last 30 minutes.

**As** an operations manager, **I want** to monitor the percentage of customers who enter the restaurant within 2 minutes of queuing, **So that** I can evaluate queue efficiency and identify potential bottlenecks in real-time.

```jsx

This metric reflects real-time queue efficiency.
It processes q_exit_resto events where dur ≤ 120 seconds,
using only events from the most recent 30-minute window.

Calculation:
===================
get the total of evt_type: "q_exit_resto" events in last 30 minutes (intervally)
Filter dur <= 120 seconds
Divide by total q_exit_resto events to get the percentage

Threhold.
==========
Above 50 percent = Green Color
Between 40 to 49 Percent = Yellow color
Below 40 = Red Color


```
