import { config } from '@/config'
import logger from '@/lib/logger'
import type {
  TSection,
  TSectionsMetrics,
} from '@/services/section-metrics/types'
import { Section } from '@/services/section-metrics/types'
import { EventData, EventType } from '@/types/eventData.types'
import * as dateUtils from '@/utils/dateFormat.utils'
import { FieldValue, increment } from 'firebase/firestore'
import debounce from 'lodash/debounce'
import {
  ensureDocumentExists,
  firstEntryCache,
  updateMetricsDocument,
} from '../document.service'

// Define updates type for Firestore merge operations
type Updates = Record<string, unknown | FieldValue>

let pendingUpdates: Record<
  TSection,
  { events: EventData[]; earliestTimestamp: Date | null }
> = {
  [Section.ELEVATORS]: { events: [], earliestTimestamp: null },
  [Section.KOPITIAM]: { events: [], earliestTimestamp: null },
  [Section.RESTO]: { events: [], earliestTimestamp: null },
}

const updateCounts = async () => {
  // Immediately capture the current pending updates and reset the shared state.
  // Use a deep copy to avoid mutation issues. Consider structuredClone if available/preferred.
  const updatesToProcess = JSON.parse(
    JSON.stringify(pendingUpdates)
  ) as typeof pendingUpdates
  pendingUpdates = {
    [Section.ELEVATORS]: { events: [], earliestTimestamp: null },
    [Section.KOPITIAM]: { events: [], earliestTimestamp: null },
    [Section.RESTO]: { events: [], earliestTimestamp: null },
  }

  try {
    logger.debug('Starting updateCounts cycle:', {
      // Log the time the cycle *actually* starts processing
      time: dateUtils.toISOString(new Date()),
      // Log what is being processed in *this* cycle
      processingCount: {
        elevators: updatesToProcess[Section.ELEVATORS].events.length,
        kopitiam: updatesToProcess[Section.KOPITIAM].events.length,
        resto: updatesToProcess[Section.RESTO].events.length,
      },
    })

    const updates: Updates = {}

    // Ensure the document exists (no change needed here)
    const currentData: TSectionsMetrics | null = await ensureDocumentExists()
    if (!currentData) {
      logger.warn('Skipping count update - document not found')
      // Resetting happened above, so just return
      return null
    }

    const currentDate = dateUtils.formatDateOnly(new Date())

    // Process the *captured* updates (updatesToProcess), not the live pendingUpdates
    for (const section of [
      Section.ELEVATORS,
      Section.KOPITIAM,
      Section.RESTO,
    ]) {
      // Use the copied data
      const events = updatesToProcess[section].events
      const earliestTs = updatesToProcess[section].earliestTimestamp

      if (!events.length) continue

      // Handle elevators differently from other sections
      if (section === Section.ELEVATORS) {
        // Use events.length from the copied data
        if (events.length > 0) {
          // Only add update if there's a change
          updates['elevators.totalCount'] = increment(events.length)
        }
      } else {
        // Use events.length from the copied data
        if (events.length > 0) {
          // Only add update if there's a change
          updates[`${section}.totalEntry`] = increment(events.length)
        }

        // Handle first entry logic (use earliestTs from copied data)
        if (earliestTs) {
          // Update first entry if it's from a new day or earlier than existing
          const earliest = dateUtils.toTZDate(earliestTs)
          // const eventTime = dateUtils.toISOString(earliest)
          let firstEntry = currentData[section].firstEntry

          // Check if firstEntry is from the previous day
          if (firstEntry) {
            // Ensure firstEntry is treated as a Date string for consistent formatting
            const firstEntryDate = dateUtils.formatDateOnly(firstEntry)
            // Reset first entry for new day
            if (firstEntryDate !== currentDate) {
              // Reset firstEntry for the new day
              firstEntry = null
              updates[`${section}.firstEntry`] = firstEntry
              firstEntryCache[section] = firstEntry
            }
          }

          // Update if no first entry or new entry is earlier
          // Ensure comparison happens correctly (Date vs Date)
          if (
            firstEntry === null ||
            dateUtils.isDateBefore(earliest, firstEntry) // Ensure comparison with Date object
          ) {
            updates[`${section}.firstEntry`] = earliest // Store Date object directly instead of ISO string
            firstEntryCache[section] = earliest
          }
        }
      }
    }

    // Update Firestore if there are changes
    if (Object.keys(updates).length > 0) {
      updates.updatedAt = new Date()
      await updateMetricsDocument(updates)
      logger.debug('Firestore updated successfully.', {
        updatesCount: Object.keys(updates).length,
      })
    } else {
      logger.debug('No updates to send to Firestore for this cycle.')
    }
  } catch (err) {
    logger.error('Error during updateCounts cycle:', { error: err })
    // Decide if you want to re-throw or handle differently
    // Rethrowing might stop later processing if not caught upstream
    // Consider *not* re-throwing if one cycle failing shouldn't stop the service
    // throw err;
  }
}

// Debounced version to batch updates
const debouncedUpdateCounts = debounce(updateCounts, config.debounce.delayMs, {
  maxWait: config.debounce.maxWaitMs,
})

export async function handleSectionEvent(eventData: EventData) {
  try {
    const event = eventData
    let location: TSection | undefined
    switch (event.evt_type) {
      case EventType.ELEV_EXIT:
        if (event.loc === 'elev_1' || event.loc === 'elev_2') {
          location = Section.ELEVATORS
        }
        break
      case EventType.KOPI_ENTRY:
        if (event.loc === 'kopi') {
          location = Section.KOPITIAM
        }
        break
      case EventType.RESTO_ENTRY:
      case EventType.Q_EXIT_RESTO:
        if (event.loc === 'resto') {
          location = Section.RESTO
        }
        break
    }
    if (!location) return
    // Add event to pending updates
    pendingUpdates[location].events.push(event)

    // Update earliest timestamp if needed
    const eventTs = event.ts
    if (!pendingUpdates[location].earliestTimestamp) {
      pendingUpdates[location].earliestTimestamp = eventTs
    } else {
      const currentEarliest = pendingUpdates[location].earliestTimestamp as Date
      if (dateUtils.isDateBefore(eventTs, currentEarliest)) {
        pendingUpdates[location].earliestTimestamp = eventTs
      }
    }

    // Trigger debounced update
    debouncedUpdateCounts()
    logger.debug('Debounce triggered at:', {
      time: dateUtils.toISOString(new Date()),
    })
  } catch (err) {
    logger.error('Error handling section event:', { error: err })
  }
}
