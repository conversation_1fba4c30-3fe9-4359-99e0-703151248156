import { config } from '@/config'
import logger from '@/lib/logger'
import { RabbitMQReceiver } from '@/services/rabbitmq-consumer.service'
import { setupFirebaseListeners } from '@/services/update-firebase.service'
import { getWhatsAppService } from './services/whatsapp.service'
import * as schedule from 'node-schedule'
import { db } from '@/database/db'
import { sql } from 'drizzle-orm'
import { authenticateWithCustomToken, dbFirebase } from '@/firebase/dbFirebase'
import { getDoc, doc } from 'firebase/firestore'
import { appError, ERRORS } from './utils/errors'
import {
  updateJobsFromOperatingHours,
  cleanupAllJobs,
} from './services/job-scheduler.service'

type ApplicationState = {
  rabbitMQReceiver: RabbitMQReceiver | null
  activeJobs: { [key: string]: schedule.Job }
  whatsAppService: ReturnType<typeof getWhatsAppService> | null
}

const setupApplicationState = (): ApplicationState => ({
  rabbitMQReceiver: null,
  activeJobs: {},
  whatsAppService: null,
})

const gracefulShutdown = async (
  state: ApplicationState,
  exitCode: number = 0
) => {
  logger.info(
    `Received shutdown signal. Initiating graceful shutdown with exit code ${exitCode}...`
  )
  let hasErrors = false

  // Stop RabbitMQ receiver
  if (state.rabbitMQReceiver) {
    try {
      await state.rabbitMQReceiver.stop()
      logger.info('RabbitMQ receiver stopped successfully')
    } catch (err) {
      hasErrors = true
      logger.error('Failed to stop RabbitMQ receiver', {
        error: appError(ERRORS.RABBITMQ.STOP, err as Error),
      })
    }
  }

  // Clean up all jobs
  try {
    cleanupAllJobs()
    logger.info('All scheduled jobs cleaned up successfully')
  } catch (err) {
    hasErrors = true
    logger.error('Failed to clean up jobs', {
      error: appError(ERRORS.JOBS.CLEANUP, err as Error),
    })
  }

  // Shutdown WhatsApp service if enabled
  if (config.whatsapp.enabled) {
    if (state.whatsAppService !== null) {
      try {
        await state.whatsAppService.shutdown()
        logger.info('WhatsApp service shut down successfully')
      } catch (err) {
        hasErrors = true
        logger.error('Failed to shut down WhatsApp service', {
          error: appError(ERRORS.WHATSAPP.SHUTDOWN, err as Error),
        })
      }
    } else {
      logger.info(
        'WhatsApp service not initialized, skipping WhatsApp shutdown step.'
      )
    }
  }

  // If there were errors during shutdown, use a non-zero exit code
  if (hasErrors) {
    logger.error('All Graceful shutdown completed with errors', {
      error: appError(ERRORS.SHUTDOWN.PARTIAL),
    })
    process.exit(exitCode || 1)
  } else {
    logger.info('All Graceful shutdown completed successfully')
    process.exit(exitCode)
  }
}
const startApplication = async () => {
  const state = setupApplicationState()
  try {
    logger.info('✅ Initializing Firebase Admin SDK...')
    try {
      await authenticateWithCustomToken(config.firebase.uidAccount)
    } catch (err) {
      const context = err instanceof Error ? err : String(err)
      throw appError(ERRORS.FIREBASE.AUTH, context)
    }

    logger.info('Checking database connection...')
    try {
      await db.execute(sql`SELECT 1;`)
    } catch (err) {
      throw appError(ERRORS.DATABASE.CONNECTION, err as Error)
    }
    logger.info('✅ Database connection successful.')

    if (config.whatsapp.enabled) {
      logger.info('Initializing WhatsApp service...')
      try {
        state.whatsAppService = getWhatsAppService()
        await state.whatsAppService.initialize()
      } catch (err) {
        const context = err instanceof Error ? err : String(err)
        throw appError(ERRORS.WHATSAPP.INIT, context)
      }
    }
    logger.info('Starting RabbitMQ receiver...')
    try {
      state.rabbitMQReceiver = new RabbitMQReceiver()
      // Pass true to throw on error at application startup failure
      await state.rabbitMQReceiver.start(true)
    } catch (err) {
      throw appError(ERRORS.RABBITMQ.START, err as Error)
    }

    logger.info('Checking Firestore connection...')
    try {
      await getDoc(doc(dbFirebase, 'stores', config.tenantId))
      logger.info(
        `✅ Firestore connection successful for Project: ${config.firebase.projectId}, Tenant: ${config.tenantId}`
      )
    } catch (err) {
      const context = err instanceof Error ? err : String(err)
      throw appError(ERRORS.FIREBASE.FIRESTORE, context)
    }

    try {
      setupFirebaseListeners(() => {
        try {
          updateJobsFromOperatingHours()
        } catch (err) {
          logger.error('Failed to update jobs from operating hours', {
            error: appError(ERRORS.JOBS.OPERATING_HOURS, err as Error),
          })
        }
      })
      logger.info(
        'Operational hours listener configured. Jobs will be updated when schedule changes.'
      )
    } catch (err) {
      throw appError(ERRORS.FIREBASE.LISTENER, err as Error)
    }

    logger.info(
      '✅ Application started successfully and all services initialized.'
    )

    process.on('SIGINT', () => gracefulShutdown(state))
    process.on('SIGTERM', () => gracefulShutdown(state))
  } catch (err) {
    logger.error('Application failed to start', { error: err })
    // Ensure cleanup on startup failure
    await gracefulShutdown(state, 1)
  }
}

// Start the application
startApplication().catch((err) => {
  logger.error('Unhandled error during application startup', { error: err })
  process.exit(1)
})
