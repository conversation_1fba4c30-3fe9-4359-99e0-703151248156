import type { Config } from 'drizzle-kit'
import { config } from './src/config'
import fs from 'fs'

// Determine if we're in production mode
const isProduction = config.env === 'production'

// Configure SSL options based on environment
const sslConfig =
  isProduction && config.postgresql.ssl
    ? {
        ssl: {
          rejectUnauthorized: true,
          ca: config.postgresql.sslCertPath
            ? fs.readFileSync(config.postgresql.sslCertPath).toString()
            : undefined,
        },
      }
    : { ssl: false }

export default {
  dialect: 'postgresql',
  schema: './src/database/schema.ts',
  out: './src/migration',
  dbCredentials: {
    host: config.postgresql.host,
    port: config.postgresql.port,
    user: config.postgresql.user,
    password: config.postgresql.password,
    database: config.postgresql.database,
    ...sslConfig,
  },
} satisfies Config
