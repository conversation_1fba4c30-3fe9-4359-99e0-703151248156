# Environment
NODE_ENV=development

# Server
PORT=7000

# CORS
CORS_ORIGIN=http://localhost:7000

# Logging
LOG_LEVEL=info

# Tenant ID
TENANT_ID=clove

# Validation
SKIP_ENV_VALIDATION=false

# RabbitMQ
RABBITMQ_PROTOCOL=amqp
RABBITMQ_HOSTNAME=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_VHOST=/
RABBITMQ_HEARTBEAT=60
RABBITMQ_QUEUE_NAME=QUEUE_A
RABBITMQ_INITIAL_RETRY_DELAY_MS=20000
RABBITMQ_MAX_RETRY_DELAY_MS=600000 # 10 minutes

# Postgres
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DATABASE=postgres
POSTGRES_SSL_ENABLED=false
POSTGRES_SSL_CERT_PATH=/path/to/certificate.crt

# Interval for Firebase metrics and summary updates
STORE_METRICS_UPDATE_INTERVAL="15"
QUEUE_METRICS_UPDATE_INTERVAL="15"
QUEUE_SUMMARY_UPDATE_INTERVAL="15"
SECTIONS_METRICS_UPDATE_INTERVAL="15"
DAILY_RESET_TIME="17:30"

# Operational Hours Configuration
TIMEZONE=Asia/Singapore
OPERATING_HOUR_BUFFER_MINUTES=30
OPERATIONAL_HOURS_CHECK_INTERVAL_MS=3600000 # 1 hour

# Time Slot Configuration
TIME_SLOT_MINUTES=30 
# Rolling Window Configuration
ROLLING_WINDOW_MINUTES=30

# Debounce Configuration for Event Handling
DEBOUNCE_DELAY_MS=1500 # 1.5 second
DEBOUNCE_MAX_WAIT_MS=10000 # 10 seconds

# WhatsApp Config
WHATSAPP_ENABLED=false
WHATSAPP_NOTIFICATION_RECIPIENTS=**********

# Firebase (david)
FIREBASE_API_KEY=
FIREBASE_AUTH_DOMAIN=
FIREBASE_PROJECT_ID=
FIREBASE_STORAGE_BUCKET=
FIREBASE_MESSAGING_SENDER_ID=
FIREBASE_APP_ID=
FIREBASE_MEASUREMENT_ID=
FIREBASE_SERVICE_ACCOUNT_PATH=
FIREBASE_UID_ACCOUNT=

QUEUE_THRESHOLD_MULTIPLIER=5
QUEUE_ALERT_EXPIRY_MONTHS=3
