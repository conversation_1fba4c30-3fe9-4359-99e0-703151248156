import logger from '@/lib/logger'
import {
  sectionsMetricsData,
  storeMetricsData,
  queueSummariesData,
  queueMetricsData,
} from '@/utils/resetData'
import { resetCollectionDocument } from '@/utils/resetUtil'

export async function resetAllMetricsFirebase() {
  try {
    logger.info('Starting reset of all metrics collections...')

    await resetCollectionDocument(
      'sectionsMetrics',
      'clove',
      sectionsMetricsData
    )
    logger.info('✓ sectionsMetrics reset complete')

    await resetCollectionDocument('storeMetrics', 'clove', storeMetricsData)
    logger.info('✓ storeMetrics reset complete')

    await resetCollectionDocument('queueSummaries', 'clove', queueSummariesData)
    logger.info('✓ queueSummaries reset complete')

    await resetCollectionDocument('queueMetrics', 'clove', queueMetricsData)
    logger.info('✓ queueMetrics reset complete')

    logger.info('All metrics collections successfully reset!')
  } catch (error) {
    logger.error('Error in reset all metrics script:', error)
  }
}
