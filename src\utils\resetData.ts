// Collection reset data definitions
const TENANT_ID = 'clove'

export const sectionsMetricsData = {
  tenantId: TENANT_ID,
  resto: {
    firstEntry: null,
    totalEntry: 0,
    peakEntry: 0,
    peakTime: null,
  },
  kopitiam: {
    firstEntry: null,
    totalEntry: 0,
    peakEntry: 0,
    peakTime: null,
  },
  elevators: {
    totalCount: 0,
  },
  updatedAt: null,
}

export const storeMetricsData = {
  entryStats: {
    last30m: 0,
    overall: 0,
    peak: {
      time: null,
      value: 0,
    },
  },
  tenantId: TENANT_ID,
  updatedAt: null,
}

export const queueSummariesData = {
  avgEntryTime: {
    value: 0,
    percentage: null,
  },
  avgServiceTime: {
    value: 0,
  },
  tenantId: TENANT_ID,
  updatedAt: null,
}

export const queueMetricsData = {
  entryQueue: {
    avgDwellTime: 0,
    inQueue: 0,
  },
  seatingQueue: {
    avgDwellTime: 0,
    inQueue: 0,
  },
  tenantId: TENANT_ID,
  updatedAt: null,
}
