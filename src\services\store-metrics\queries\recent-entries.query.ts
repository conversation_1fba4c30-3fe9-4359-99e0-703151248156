import { db } from '@/database/db'
import { events } from '@/database/schema'
import { and, gte, inArray, sql, eq } from 'drizzle-orm'
import * as dateUtils from '@/utils/dateFormat.utils'
import logger from '@/lib/logger'
import { doc, updateDoc } from 'firebase/firestore'
import { dbFirebase } from '@/firebase/dbFirebase'
import { config } from '@/config'

// Constants for Firestore collection and document
const COLLECTION_NAME = 'storeMetrics'
const DOCUMENT_ID = 'clove'

// Cache to store the last computed value to avoid unnecessary Firestore updates
let cachedRecentEntries: number | null = null

/**
 * Retrieves the total number of entries in the recent time window
 * @param rolingWindowMins Optional time window in minutes, defaults to config.time.rollingWindowMinutes
 * @returns Promise<number> Total count of entries in the specified time window
 */
export async function getRecentEntries(
  rolingWindowMins: number = config.time.rollingWindowMinutes
): Promise<number> {
  try {
    const windowStartTime = dateUtils.getRollingWindowStart(rolingWindowMins)
    // First, get the total entries
    const entriesResult = await db
      .select({
        entry_count: sql<string>`count(*)`.as('entry_count'),
      })
      .from(events)
      .where(
        and(
          gte(events.ts, windowStartTime),
          inArray(events.evt_type, [
            'q_exit_resto',
            'kopi_entry',
            'resto_entry',
          ])
        )
      )

    // Then, get the total duplicate entries to subtract
    const duplicatesResult = await db
      .select({
        duplicate_count: sql<string>`COALESCE(SUM(${events.count}), 0)`.as(
          'duplicate_count'
        ),
      })
      .from(events)
      .where(
        and(
          gte(events.ts, windowStartTime),
          eq(events.evt_type, 'du_kopi_entry')
        )
      )

    const totalEntries = parseInt(entriesResult[0]?.entry_count ?? '0', 10)
    const duplicateEntries = parseInt(
      duplicatesResult[0]?.duplicate_count ?? '0',
      10
    )

    // Subtract duplicates from total
    return Math.max(0, totalEntries - duplicateEntries)
  } catch (error) {
    logger.error('Error fetching recent entries:', { error })
    throw error
  }
}

/**
 * Updates the Firestore document with the recent entries count
 * Only updates if the value has changed from the previous update
 * Uses the time window specified in config.recentEntriesWindowMinutes
 */
export async function updateRecentEntries(): Promise<void> {
  try {
    const recentEntries = await getRecentEntries()
    logger.debug('RecentEntries Query Executed')

    // Check if the value has changed
    if (cachedRecentEntries === recentEntries) {
      logger.debug(
        `No change in recent entries (${config.time.rollingWindowMinutes} min window), skipping update.`
      )
      return
    }

    // Update Firestore with the new value
    const metricsRef = doc(dbFirebase, COLLECTION_NAME, DOCUMENT_ID)
    const updateData = {
      'entryStats.last30m': recentEntries,
      // tenantId: config.tenantId,
      // updatedAt: dateUtils.toISOString(new Date()),
    }

    await updateDoc(metricsRef, updateData)

    // Update the cache
    cachedRecentEntries = recentEntries
    logger.info(
      `Updated recent entries (${config.time.rollingWindowMinutes} min window) in Firestore`,
      {
        recentEntries,
      }
    )
  } catch (error) {
    logger.error('Error updating last 30 minute entries:', { error })
  }
}
