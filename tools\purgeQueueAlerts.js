#!/usr/bin/env node
const { initializeApp } = require('firebase/app')
const {
  getFirestore,
  collection,
  getDocs,
  deleteDoc,
  doc,
  Timestamp,
} = require('firebase/firestore')
const readline = require('readline')

// Firebase configurations
const firebaseConfigs = require('./purgeFireBaseConfig.json')

// CLI Interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
})

async function purgeOldQueueAlerts(environmentKey) {
  try {
    console.log(
      `\nInitializing Firebase with ${environmentKey} configuration...`
    )
    const app = initializeApp(firebaseConfigs[environmentKey])
    const db = getFirestore(app)

    const threeMonthsAgo = new Date()
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3)

    console.log('Fetching queueAlerts collection...')
    const queueAlertsRef = collection(db, 'queueAlerts')
    const querySnapshot = await getDocs(queueAlertsRef)

    let deletedCount = 0
    console.log(`Found ${querySnapshot.docs.length} queue alerts`)

    for (const docSnapshot of querySnapshot.docs) {
      const alertData = docSnapshot.data()
      let alertTimestamp

      // Handle the timestamp correctly based on its format
      if (alertData.timestamp instanceof Timestamp) {
        // If it's a Firestore Timestamp object, use toDate()
        alertTimestamp = alertData.timestamp.toDate()
      } else if (alertData.timestamp && alertData.timestamp.seconds) {
        // If it's a Timestamp-like object with seconds
        alertTimestamp = new Date(alertData.timestamp.seconds * 1000)
      } else {
        // Otherwise try to parse it directly
        alertTimestamp = new Date(alertData.timestamp)
      }

      if (alertTimestamp < threeMonthsAgo) {
        await deleteDoc(doc(db, 'queueAlerts', docSnapshot.id))
        console.log(`Deleted queue alert: ${docSnapshot.id}`)
        deletedCount++
      }
    }

    console.log(`\nPurge complete! Deleted ${deletedCount} old queue alerts.`)
    return true
  } catch (error) {
    console.error('Error purging old queue alerts:', error)
    return false
  }
}

function displayMenu() {
  console.clear()
  console.log('=====================================')
  console.log('   FIREBASE QUEUE ALERTS PURGE UTILITY   ')
  console.log('=====================================\n')
  console.log('Select Firebase environment:')
  console.log('1. Development')
  console.log('2. Staging')
  console.log('3. Production')
  console.log('4. Exit\n')

  rl.question('Enter your choice (1-4): ', async (answer) => {
    switch (answer) {
      case '1':
        console.log(
          '\n⚠️  WARNING: You are about to purge old queue alerts in the DEVELOPMENT environment'
        )
        confirmPurge('dev')
        break
      case '2':
        console.log(
          '\n⚠️  WARNING: You are about to purge old queue alerts in the STAGING environment'
        )
        confirmPurge('staging')
        break
      case '3':
        console.log(
          '\n⚠️  WARNING: You are about to purge old queue alerts in the PRODUCTION environment'
        )
        console.log('⚠️  THIS WILL AFFECT LIVE DATA! BE CAREFUL!')
        confirmPurge('production')
        break
      case '4':
        console.log('\nExiting program')
        rl.close()
        break
      default:
        console.log('\nInvalid option. Please try again.')
        setTimeout(displayMenu, 1500)
        break
    }
  })
}

function confirmPurge(env) {
  rl.question(
    `\nAre you sure you want to proceed with purging old queue alerts in ${env.toUpperCase()}? (y/n): `,
    async (confirmation) => {
      if (confirmation.toLowerCase() === 'y') {
        console.log(`\nPurging old queue alerts in ${env.toUpperCase()}...`)

        try {
          const success = await purgeOldQueueAlerts(env)
          if (success) {
            console.log('\nPurge complete! Exiting...')
            // Exit with success code
            process.exit(0)
          } else {
            console.error('\nFailed to purge queue alerts. Exiting...')
            // Exit with error code
            process.exit(1)
          }
        } catch (error) {
          console.error('\nFailed to purge queue alerts:', error)
          // Exit with error code
          process.exit(1)
        }
      } else {
        console.log('\nPurge cancelled.')
        setTimeout(displayMenu, 1500)
      }
    }
  )
}

// Start the application
displayMenu()
