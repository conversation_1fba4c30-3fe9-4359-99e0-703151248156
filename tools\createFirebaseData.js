#!/usr/bin/env node
const { initializeApp } = require('firebase/app')
const { getFirestore, doc, setDoc } = require('firebase/firestore')
const readline = require('readline')

// Firebase configurations
const firebaseConfig = require('./createFireBaseConfig.json')

// Import data configurations
const {
  sectionsMetricsData,
  storeMetricsData,
  queueSummariesData,
  queueMetricsData,
  queueAlertsData,
  storesData,
  thresholdsData,
} = require('./dataConfig')

// CLI Interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
})

// Function to create a collection document
async function createCollectionDocument(db, collectionName, documentId, data) {
  try {
    // Always ensure updatedAt is current
    if (data.updatedAt !== undefined) {
      data.updatedAt = new Date()
    }

    const docRef = doc(db, collectionName, documentId)
    await setDoc(docRef, data)
    console.log(
      `✓ Successfully created ${collectionName} document for ${documentId}`
    )
  } catch (error) {
    console.error(`Error creating ${collectionName}:`, error)
    throw error
  }
}

// Main initialization function
async function initializeFirebase() {
  try {
    console.log('\nInitializing Firebase with configuration...')

    // Initialize Firebase
    const app = initializeApp(firebaseConfig)
    const db = getFirestore(app)

    console.log('\nStarting creation of all collections...')

    // Create/initialize all collections
    await createCollectionDocument(
      db,
      'sectionsMetrics',
      'clove',
      sectionsMetricsData
    )

    await createCollectionDocument(
      db,
      'storeMetrics',
      'clove',
      storeMetricsData
    )

    await createCollectionDocument(
      db,
      'queueSummaries',
      'clove',
      queueSummariesData
    )

    await createCollectionDocument(
      db,
      'queueMetrics',
      'clove',
      queueMetricsData
    )

    await createCollectionDocument(
      db,
      'queueAlerts',
      'alert_' + Date.now(),
      queueAlertsData
    )

    await createCollectionDocument(db, 'stores', 'clove', storesData)

    await createCollectionDocument(db, 'thresholds', 'clove', thresholdsData)

    console.log('\nAll collections successfully created!')
    return true
  } catch (error) {
    console.error('\nError in Firebase initialization:', error)
    return false
  }
}

function displayMenu() {
  console.clear()
  console.log('=========================================')
  console.log('   FIREBASE COLLECTIONS CREATION UTILITY   ')
  console.log('=========================================\n')
  console.log('This utility will create the following collections:')
  console.log('- sectionsMetrics')
  console.log('- storeMetrics')
  console.log('- queueSummaries')
  console.log('- queueMetrics')
  console.log('- queueAlerts')
  console.log('- stores')
  console.log('- thresholds\n')
  console.log(
    '⚠️  WARNING: This will create collections in your Firebase project'
  )
  console.log(
    '⚠️  Existing data may be overwritten if documents already exist\n'
  )

  rl.question(
    'Are you sure you want to proceed? (y/n): ',
    async (confirmation) => {
      if (confirmation.toLowerCase() === 'y') {
        console.log('\nInitializing Firebase data...')

        try {
          const success = await initializeFirebase()
          if (success) {
            console.log('\nInitialization complete! Exiting...')
            process.exit(0)
          } else {
            console.error('\nFailed to initialize data. Exiting...')
            process.exit(1)
          }
        } catch (error) {
          console.error('\nFailed to initialize data:', error)
          process.exit(1)
        }
      } else {
        console.log('\nInitialization cancelled.')
        rl.close()
      }
    }
  )
}

// Start the program
displayMenu()
