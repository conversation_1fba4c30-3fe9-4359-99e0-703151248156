import dotenv from 'dotenv'
import fs from 'fs'
import { z } from 'zod'

dotenv.config()

const result = dotenv.config()
if (result.error) {
  console.error('❌ Failed to load .env file', result.error)
  process.exit(1)
}

// Define environment schema
const envSchema = z.object({
  NODE_ENV: z
    .enum(['development', 'production', 'staging', 'test'])
    .default('development'),
  PORT: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('3000'),
  // CORS
  CORS_ORIGIN: z.string().optional().default('http://localhost:7000'),

  // Logging
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),

  // RabbitMQ
  RABBITMQ_PROTOCOL: z.string().default('amqp'),
  RABBITMQ_HOSTNAME: z.string().default('localhost'),
  RABBITMQ_PORT: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('5672'),
  RABBITMQ_USERNAME: z.string().default('guest'),
  RABBITMQ_PASSWORD: z.string().default('guest'),
  RABBITMQ_VHOST: z.string().default('/'),
  RABBITMQ_HEARTBEAT: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('60'),
  RABBITMQ_QUEUE_NAME: z.string().default('Q_SEND_CROWD_MONITORING'),
  RABBITMQ_INITIAL_RETRY_DELAY_MS: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('20000'), // 20 seconds
  RABBITMQ_MAX_RETRY_DELAY_MS: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('600000'), // 10 minutes

  // PostgreSQL
  POSTGRES_HOST: z.string().default('localhost'),
  POSTGRES_PORT: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('5432'),
  POSTGRES_USER: z.string().default('postgres'),
  POSTGRES_PASSWORD: z.string().default('password'),
  POSTGRES_DATABASE: z.string().default('postgres'),
  POSTGRES_SSL_CERT_PATH: z.string().optional(),
  POSTGRES_SSL_ENABLED: z
    .enum(['true', 'false'])
    .transform((val) => val === 'true')
    .default('false'),

  SKIP_VALIDATION: z.boolean().default(false),

  // Interval for Firebase updates
  STORE_METRICS_UPDATE_INTERVAL: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('3'),
  QUEUE_METRICS_UPDATE_INTERVAL: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('3'),
  QUEUE_SUMMARY_UPDATE_INTERVAL: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('3'),
  SECTIONS_METRICS_UPDATE_INTERVAL: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('3'),
  DAILY_RESET_TIME: z.string().default('17:30'),

  // Firebase Configuration
  FIREBASE_API_KEY: z.string(),
  FIREBASE_AUTH_DOMAIN: z.string(),
  FIREBASE_PROJECT_ID: z.string(),
  FIREBASE_STORAGE_BUCKET: z.string(),
  FIREBASE_MESSAGING_SENDER_ID: z.string(),
  FIREBASE_APP_ID: z.string(),
  FIREBASE_MEASUREMENT_ID: z.string(),
  FIREBASE_SERVICE_ACCOUNT_PATH: z
    .string()
    .default('serviceAccountKey.json')
    .refine(
      (path) => {
        try {
          return fs.existsSync(path)
        } catch (error) {
          return false
        }
      },
      {
        message:
          'Firebase service serviceAccountKey.json file does not exist at the specified path',
      }
    ),
  FIREBASE_UID_ACCOUNT: z.string().default('HSXY4FcQk1Vth0NRhdUe6IAumSa2'),

  // Timezone
  TIMEZONE: z.string().default('Asia/Singapore'),

  // Tenant
  TENANT_ID: z.string().default('clove'),

  // operating hours buffer
  OPERATING_HOUR_BUFFER_MINUTES: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('30'),

  // operational hours check interval in milliseconds
  OPERATIONAL_HOURS_CHECK_INTERVAL_MS: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('300000'), // Default to 5 minutes

  // Time slot in minutes
  TIME_SLOT_MINUTES: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('30'), // Default to 30 minutes

  // Recent time window in minutes
  ROLLING_WINDOW_MINUTES: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('30'), // Default to 30 minutes

  // Debounce configuration for event handling
  DEBOUNCE_DELAY_MS: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('1500'), // Default to 1.5 seconds

  DEBOUNCE_MAX_WAIT_MS: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('15000'), // Default to 15 seconds

  // WhatsApp Configuration
  WHATSAPP_ENABLED: z
    .enum(['true', 'false'])
    .transform((val) => val === 'true')
    .default('false'),

  WHATSAPP_NOTIFICATION_RECIPIENTS: z
    .string()
    .default('6283807275627')
    .transform((val) => val.split(',').map((number) => number.trim())),

  QUEUE_THRESHOLD_MULTIPLIER: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('5'),

  QUEUE_ALERT_EXPIRY_MONTHS: z
    .string()
    .transform((val) => parseInt(val, 10))
    .default('3'),
})

// Parse and validate environment variables
export const env = envSchema.parse(process.env)
