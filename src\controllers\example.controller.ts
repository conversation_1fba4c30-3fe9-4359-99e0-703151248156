import { Request, Response } from 'express'
import { getAllExamples } from '@/services/example.service'
import { sendSuccess, sendError } from '@/utils/response.utils'

// Controller methods
export async function getAllExamplesHandler(_req: Request, res: Response) {
  try {
    const examples = await getAllExamples()
    return sendSuccess(res, examples)
  } catch (error) {
    return sendError(res, error as Error)
  }
}
