export enum EventType {
  Q_ENTRY = 'q_entry',
  Q_EXIT = 'q_exit',
  Q_EXIT_RESTO = 'q_exit_resto',
  KOPI_ENTRY = 'kopi_entry',
  ELEV_EXIT = 'elev_exit',
  RESTO_ENTRY = 'resto_entry',
  DU_KOPI_ENTRY = 'du_kopi_entry',
  Q_ABANDON = 'q_abandon',
}

export enum QueueName {
  Q1 = 'q1',
  Q2 = 'q2',
}

export enum Location {
  ELEV_1 = 'elev_1',
  ELEV_2 = 'elev_2',
  Q1 = 'q1',
  Q2 = 'q2',
  KOPI = 'kopi',
  RESTO = 'resto',
}

export enum Camera {
  C1 = 'c1',
  C2 = 'c2',
  C3 = 'c3',
  C4 = 'c4',
  C5 = 'c5',
}

export type EventData = {
  t_id: string | null
  evt_type: EventType
  cam: Camera
  ts: Date // Changed from string to Date
  q_name?: QueueName | null
  loc: Location
  dur: number
  count: number
  tenant_id: string
}
